"""Database operations for retrieving column data by IDs and names."""

from typing import List, Dict, Tuple, Optional, Any
from ..client import supabase
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import InjectedToolArg
from typing import Annotated


def get_columns_by_table_id(
    table_id: str
) -> Tuple[Optional[List[Dict[str, Any]]], Optional[str]]:
    """Get all columns for a table including their IDs, names, and all other fields.
    
    Args:
        config: RunnableConfig containing table_id
        
    Returns:
        Tuple[Optional[List[Dict[str, Any]]], Optional[str]]: Tuple containing (data, error)
        where data is a list of all column objects if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        # Extract table_id from config using Configuration class
        # Query the columns table for all columns in the table
        response = supabase.table('columns').select('*').eq('table_id', table_id).execute()
        
        # Check if data is available
        if not hasattr(response, 'data') or response.data is None:
            return None, "Server Error: Data not available"
        
        return response.data, None
        
    except Exception as e:
        error_msg = f"Error fetching columns for table: {str(e)}"
        print(error_msg)
        return None, error_msg


def get_columns_by_names(
    table_id: str,
    column_names: List[str]
) -> Tuple[Optional[List[Dict[str, Any]]], Optional[str]]:
    """Get column IDs based on their names and table ID.
    
    Args:
        column_names (List[str]): List of column names to get IDs for
        
    Returns:
        Tuple[Optional[List[Dict[str, Any]]], Optional[str]]: Tuple containing (data, error)
        where data is a list of dictionaries with 'column_name' and 'column_id' keys if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        if not column_names:
            return [], None
            
        # Use the unified function and filter results
        all_columns, error = get_columns_by_table_id(table_id)
        if error:
            return None, error
        
        # Filter for requested column names and transform to required format
        result = []
        if all_columns:
            for column in all_columns:
                if column['name'] in column_names:
                    result.append({
                        "column_name": column['name'],
                        "column_id": column['id']
                    })
        
        return result, None
        
    except Exception as e:
        error_msg = f"Error fetching column IDs: {str(e)}"
        print(error_msg)
        return None, error_msg


def get_columns_by_ids(
    table_id: str,
    column_ids: List[int]
) -> Tuple[Optional[List[Dict[str, Any]]], Optional[str]]:
    """Get column data based on their IDs and table ID.
    
    Args:
        column_ids (List[int]): List of column IDs to get data for
        
    Returns:
        Tuple[Optional[List[Dict[str, Any]]], Optional[str]]: Tuple containing (data, error)
        where data is a list of dictionaries with all column fields if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        if not column_ids:
            return [], None
            
        # Use the unified function and filter results
        all_columns, error = get_columns_by_table_id(table_id)
        if error:
            return None, error
        
        # Filter for requested column IDs
        result = []
        if all_columns:
            for column in all_columns:
                if column['id'] in column_ids:
                    result.append(column)
        
        return result, None
        
    except Exception as e:
        error_msg = f"Error fetching columns by IDs: {str(e)}"
        print(error_msg)
        return None, error_msg
