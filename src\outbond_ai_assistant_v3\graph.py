"""Define a ReAct agent using LangGraph.

This agent follows the ReAct pattern (Reasoning and Acting) to solve tasks
by thinking step by step and using tools when needed.
"""

from langgraph.graph import StateGraph, END

from langchain_core.messages import AIMessage

from outbond_ai_assistant_v3.nodes import intent_classifier_node
from src.outbond_ai_assistant_v3.configuration import Configuration
from src.outbond_ai_assistant_v3.state import AgentState

from src.outbond_ai_assistant_v3.nodes import  tool_node, call_model, table_indexing_node


def should_continue(state: AgentState):
    """Determine whether to continue with tool execution or end the conversation."""
    messages = state["messages"]
    last_message = messages[-1]
    
    # If we're in chat mode, always end (no tools should be used)
    is_chat_mode = state.get("mode") == "chat"
    if is_chat_mode:
        return "end"
    
    # If there are tool calls, continue to the tool node
    if isinstance(last_message, AIMessage) and last_message.tool_calls:
        return "continue"
    
    # Otherwise, end the conversation
    return "end"


# Define the graph
def create_graph():
    """Create and return the ReAct agent graph."""
    # Define a new graph with our state
    workflow = StateGraph(AgentState, config_schema=Configuration)
    
    # Add nodes to the graph
    #MODIFIED
    #orkflow.add_node("intent_classifier", intent_classifier_node)
    #MODIFIED
    
    workflow.add_node("table_indexing", table_indexing_node)
    workflow.add_node("agent", call_model)
    workflow.add_node("tools", tool_node)
    
    # Set the entry point to table_indexing (runs first)
    
     #MODIFIED
    workflow.set_entry_point("table_indexing")
    #workflow.set_entry_point("intent_classifier")
    #workflow.add_edge("intent_classifier", "table_indexing")
    
     #MODIFIED
    
    #workflow.add_edge("intent_classifier", "table_indexing")
    # Add edge from table_indexing to agent
    workflow.add_edge("table_indexing", "agent")
    
    # Add conditional edges from agent node
    workflow.add_conditional_edges(
        "agent",
        should_continue,
        {
            "continue": "tools",
            "end": END,
        },
    )
    
    # Add edge from tools back to agent
    workflow.add_edge("tools", "agent")
    
    # Compile the graph (LangGraph Platform handles persistence automatically)
    graph = workflow.compile()
    graph.name = "Bond AI"  # Custom name for LangSmith
    
    return graph


# Create the graph
graph = create_graph()
