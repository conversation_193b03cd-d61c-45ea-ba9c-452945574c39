"""Database interaction functions."""

from typing import Dict, List, Optional, Tuple, Any
from supabase import Client, create_client
import os
from dotenv import load_dotenv
from src.db.optimised_utils import RealtimeEvent


# Load environment variables from .env file, force override existing ones
load_dotenv(override=True)

url: str = os.environ.get("SUPABASE_URL")
key: str = os.environ.get("SUPABASE_SERVICE_ROLE_KEY")


if not url or not key:
    raise ValueError("SUPABASE_URL and SUPABASE_KEY must be set in environment variables")

try:
    supabase: Client = create_client(url, key)
    print("Supabase client created successfully")
except Exception as e:
    print(f"Error creating Supabase client: {str(e)}")
    raise

def convert_filter_group(filter_group):
    """
    Convert filter group structure from first format to second format.
    
    Args:
        filter_group (dict): Input filter group with structure:
            {
                "type": "filterGroup",
                "operator": "and|or", 
                "filters": [...]
            }
    
    Returns:
        dict: Converted structure with format:
            {
                "operator": "AND|OR",
                "rules": [...]
            }
    """
    if not isinstance(filter_group, dict) or filter_group.get("type") != "filterGroup":
        raise ValueError("Input must be a filterGroup object")
    
    # Convert operator to uppercase
    operator = filter_group.get("operator", "and").upper()
    
    # Convert filters to rules
    rules = []
    filters = filter_group.get("filters", [])
    
    for filter_item in filters:
        if filter_item.get("type") == "condition":
            rule = {
                "column_id": int(filter_item.get("column", {}).get("id")),  # Convert to integer
                "operator": filter_item.get("operator"),
                "value": filter_item.get("value", "")
            }
            rules.append(rule)
        elif filter_item.get("type") == "filterGroup":
            # Recursively handle nested filter groups
            nested_rule = convert_filter_group(filter_item)
            rules.append(nested_rule)
    
    return {
        "operator": operator,
        "rules": rules
    }

def send_realtime_notification(channel: str, event: str, payload: Dict[str, Any]) -> None:
    """Synchronous function to send real-time notifications to Supabase.
    
    Args:
        channel: The channel to send the notification to
        event: The event type (from RealtimeEvent)
        payload: The data payload to send
    """
    try:
        response = supabase.rpc('notify_fe_generic', {
            "channel": channel,
            "event": event,
            "data": payload,
        }).execute()    
    except Exception as e:
        print(f"Failed to send realtime broadcast: {str(e)}")
        raise

def get_table_data(table_id, max_rows: int = 1, filters: Optional[Dict[str, Any]] = None, 
                  search: Optional[str] = None, sorts: Optional[Dict[str, Any]] = None, 
                  column_ids: Optional[List[int]] = None) -> Tuple[Optional[List[Dict[str, Any]]], Optional[str]]:
    """Fetch data from a specific table using Supabase RPC.
    
    Args:
        table_id (str): The ID of the table to get data from
        max_rows (int, optional): Maximum number of rows to return. Defaults to 1
        filters (Dict, optional): Filters to apply to the query. Defaults to None
        search (str, optional): Search text to filter results. Defaults to None
        sorts (Dict, optional): Sort criteria to apply. Defaults to None
        column_ids (List[int], optional): Specific column IDs to return. Defaults to None
        
    Returns:
        Tuple[Optional[List[Dict]], Optional[str]]: Tuple containing (data, error)
        where data is the table data if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        response = supabase.rpc(
            'agent_get_table_columns_with_cells_v2',
            {
                'p_table_id': table_id,
                'p_filters': filters,
                'p_search': search,
                'p_sorts': sorts,
                'p_limit': max_rows,
                'p_column_ids': column_ids
            }
        ).execute()
        
        if not hasattr(response, 'data'):
            return None, "Server Error: Data not available"
            
        if hasattr(response, 'error') and response.error:
            return None, f"Database error: {response.error.message}"
            
        return response.data, None
            
    except Exception as e:
        error_msg = f"Error fetching table data: {str(e)}"
        print(error_msg)
        return None, error_msg


def update_column_types(updates_data: List[Dict[str, Any]]) -> Tuple[bool, Optional[str]]:
    """Update column types in bulk using Supabase RPC.
    
    Args:
        updates_data (List[Dict[str, Any]]): List of updates, each containing p_table_id, p_column_id, and p_type
        
    Returns:
        Tuple[bool, Optional[str]]: Tuple containing (success, error)
        where success is True if update was successful, False if failed
        and error is the error message if failed, None if successful
    """
    try:
        response = supabase.rpc(
            'update_column_types_bulk',
            {
                "p_inputs": updates_data
            }
        ).execute()
        
        if not hasattr(response, 'data'):
            return False, "Failed to update column types"
        
        return True, None
        
    except Exception as e:
        return False, str(e)

def upsert_smart_column(
    table_id: str,
    column_name: str,
    service_id: int,
    inputs: List[Dict[str, Any]],
    parameters: List[Dict[str, str]],
    providers: List[Dict[str, List[str]]],
    column_id: Optional[int] = None
) -> Tuple[Optional[Dict], Optional[str]]:
    """Create or update a smart column in the database.
    
    Args:
        table_id: The ID of the table
        column_name: Name of the column
        service_id: ID of the service to use
        inputs: List of input mapping dictionaries
        parameters: List of parameter dictionaries (including formula)
        providers: List of provider configurations
        column_id: Optional ID of the column to update, if None, a new column is created
        
    Returns:
        Tuple[Optional[Dict], Optional[str]]: Tuple containing (data, error)
        where data is the response if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        print(f"[DEBUG] Starting upsert_smart_column for table_id: {table_id}, column_name: {column_name}")
        print(f"[DEBUG] service_id: {service_id}, column_id: {column_id}")
        
        # Construct complete settings payload
        complete_settings = {
            "inputs": inputs,
            "providers": providers,
            "parameters": parameters
        }
        
        # Prepare parameters for RPC call
        rpc_params = {
            "p_table_id": table_id,
            "p_name": column_name,
            "p_service_id": service_id,
            "p_settings": complete_settings,
        }
        
        # Add column_id if provided (for updates)
        if column_id is not None:
            rpc_params["p_id"] = column_id
            print(f"[DEBUG] Updating existing column with ID: {column_id}")
        else:
            print(f"[DEBUG] Creating new column")
            
        print(f"[DEBUG] Calling upsert_smart_column RPC with params: {rpc_params}")
        response = supabase.rpc(
            'upsert_smart_column',
            rpc_params
        ).execute()
        
        if not hasattr(response, 'data'):
            print(f"[DEBUG] ERROR: No data attribute in response")
            return None, "Failed to create or update smart column"
        
        print("response.data  ----> ", response.data)
        
        # Determine if this was an insert or update based on column_id parameter
        event_type = RealtimeEvent.COLUMN_UPDATE if column_id is not None else RealtimeEvent.COLUMN_INSERT
        print(f"[DEBUG] Sending realtime notification with event_type: {event_type}")
        
        # Send real-time notification
        send_realtime_notification(table_id, event_type, response.data)
        
        return response.data, None
        
    except Exception as e:
        print(f"[DEBUG] Exception in upsert_smart_column: {str(e)}")
        return None, str(e)

def db_run_column(
    table_id: str,
    column_id: str,
    filters: Optional[List[Dict[str, Any]]] = None,
    sorts: Optional[List[Dict[str, Any]]] = None,
    count: Optional[int] = None,
    row_ids: Optional[List[int]] = None
) -> Tuple[bool, Optional[str]]:
    """Run a specific column in a table using Supabase RPC.
    
    This function triggers the execution of a smart column for the specified table.
    
    Args:
        table_id (str): The ID of the table containing the column
        column_id (str): The ID of the column to run
        filters (Optional[List[Dict[str, Any]]], optional): Filters to apply. Defaults to None.
        sorts (Optional[List[Dict[str, Any]]], optional): Sort criteria to apply. Defaults to None.
        count (Optional[int], optional): Number of rows to process. Defaults to None.
        row_ids (Optional[List[int]], optional): Specific row IDs to process. Defaults to None.
        
    Returns:
        Tuple[bool, Optional[str]]: Tuple containing (success, error)
        where success is True if the column was run successfully, False if failed
        and error is the error message if failed, None if successful
    """
    try:
        print(f"[DEBUG] db_run_column called with:")
        print(f"[DEBUG] - table_id: {table_id} (type: {type(table_id)})")
        print(f"[DEBUG] - column_id: {column_id} (type: {type(column_id)})")
        print(f"[DEBUG] - filters: {filters} (type: {type(filters)})")
        print(f"[DEBUG] - sorts: {sorts} (type: {type(sorts)})")
        print(f"[DEBUG] - count: {count} (type: {type(count)})")
        print(f"[DEBUG] - row_ids: {row_ids} (type: {type(row_ids)})")
        
        rpc_params = {
            'p_table_id': table_id,
            'p_column_id': column_id,
            'p_filters': filters,
            'p_sorts': sorts,
            'p_count': count,
            'p_row_ids': row_ids
        }
        
        print(f"[DEBUG] About to call supabase.rpc('run_column') with params: {rpc_params}")
        
        response = supabase.rpc('run_column', rpc_params).execute()
        
        print(f"[DEBUG] supabase.rpc('run_column') returned:")
        print(f"[DEBUG] - response: {response}")
        print(f"[DEBUG] - response.data: {getattr(response, 'data', 'No data attribute')}")
        print(f"[DEBUG] - response.error: {getattr(response, 'error', 'No error attribute')}")
        
        # Check if there was an error in the response
        if hasattr(response, 'error') and response.error is not None:
            print(f"[DEBUG] Error in response: {response.error}")
            if hasattr(response.error, 'message'):
                print(f"[DEBUG] Error message: {response.error.message}")
                return False, response.error.message
            else:
                print(f"[DEBUG] Error without message: {response.error}")
                return False, str(response.error)
        
        # If we get here, the operation was successful
        print(f"[DEBUG] db_run_column operation successful")
        return True, None
        
    except Exception as e:
        print(f"[DEBUG] Exception in db_run_column: {e}")
        print(f"[DEBUG] Exception type: {type(e)}")
        import traceback
        print(f"[DEBUG] Full traceback: {traceback.format_exc()}")
        return False, str(e)

def db_get_runnable_columns(table_id: str) -> Tuple[Optional[List[Dict[str, Any]]], Optional[str]]:
    """Fetch runnable columns for a specific table from the table_columns view.
    
    This function retrieves columns that can be run (is_runnable=True) for the specified table.
    
    Args:
        table_id (str): The ID of the table to get runnable columns for
        
    Returns:
        Tuple[Optional[List[Dict[str, Any]]], Optional[str]]: Tuple containing (data, error)
        where data is the list of runnable columns if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        response = supabase.from_('table_columns').select('id, type, name') \
            .eq('table_id', table_id) \
            .eq('is_runnable', True) \
            .execute()
        
        # Check if there was an error in the response
        if hasattr(response, 'error') and response.error is not None:
            return None, response.error.message
        
        # Check if data is available
        if not hasattr(response, 'data'):
            return None, "No data available from table_columns view"
        
        return response.data, None
        
    except Exception as e:
        return None, f"Error fetching runnable columns: {str(e)}" 

def get_table_filters(table_id: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
    """Fetch only the filters field from a specific table.
    
    Args:
        table_id (str): The ID of the table to get filters for
        
    Returns:
        Tuple[Optional[Dict], Optional[str]]: Tuple containing (filters_data, error)
        where filters_data is the filters field if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        # Get only the filters field
        response = supabase.from_('tables').select('filters').eq('id', table_id).single().execute()
        
        # Check for errors
        if hasattr(response, 'error') and response.error is not None:
            return None, response.error.message
        
        # Check if data is available
        if not hasattr(response, 'data') or 'filters' not in response.data:
            return None, "No filters data available for this table"
        
        # Return just the filters field
        return response.data.get('filters'), None
            
    except Exception as e:
        error_msg = f"Error fetching table filters: {str(e)}"
        print(error_msg)
        return None, error_msg

def update_table_filters(table_id: str, filters_value: Dict[str, Any]) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
    """Update only the filters field for a specific table.
    
    Args:
        table_id (str): The ID of the table to update filters for
        filters_value (Dict[str, Any]): The new value for the filters field
        
    Returns:
        Tuple[Optional[Dict], Optional[str]]: Tuple containing (updated_data, error)
        where updated_data is the updated table data if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        # Update only the filters field - do not chain .select() as it's causing errors
        update_response = supabase.from_('tables').update({"filters": filters_value}).eq('id', table_id).execute()
        
        # Check for errors in the update
        if hasattr(update_response, 'error') and update_response.error is not None:
            return None, update_response.error.message
        
        # Now fetch the updated data in a separate query
        select_response = supabase.from_('tables').select('id, filters').eq('id', table_id).execute()
        
        # Check for errors in the select
        if hasattr(select_response, 'error') and select_response.error is not None:
            return None, select_response.error.message
        
        # Check if data is available
        if not hasattr(select_response, 'data') or not select_response.data:
            return None, "No data available after update"
        
        # Send real-time notification for filter update
        updated_data = select_response.data[0]
        send_realtime_notification(table_id, RealtimeEvent.FILTER_UPDATE, {
            "table_id": table_id,
            "filters": filters_value
        })
        
        return updated_data, None
            
    except Exception as e:
        error_msg = f"Error updating table filters: {str(e)}"
        print(error_msg)
        return None, error_msg

def get_table_columns_info(table_id: str) -> Tuple[Optional[List[Dict[str, Any]]], Optional[str]]:
    """Fetch columns for a table and check description status.
    
    Args:
        table_id (str): The ID of the table to get columns from
        
    Returns:
        Tuple[Optional[List[Dict[str, Any]]], Optional[str]]: Tuple containing (columns_data, error)
        where columns_data is a list of column info with description status if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        # Fetch columns with id, name, and description
        response = supabase.table('columns').select('id,name,description,table_id').eq('table_id', table_id).execute()
        
        if not hasattr(response, 'data'):
            return None, "Server Error: Data not available"
            
        if hasattr(response, 'error') and response.error:
            return None, f"Database error: {response.error.message}"
        
        # Process the columns to add metadata about description status
        columns_with_status = []
        for column in response.data:
            column_info = {
                'id': column['id'],
                'name': column['name'],
                'description': column['description'],
                'has_description': column['description'] is not None and column['description'].strip() != '',
                'needs_description': column['description'] is None or column['description'].strip() == ''
            }
            columns_with_status.append(column_info)
            
        return columns_with_status, None
            
    except Exception as e:
        error_msg = f"Error fetching table columns: {str(e)}"
        print(error_msg)
        return None, error_msg

def db_run_sql_query(sql_query: str) -> Tuple[Optional[List[Dict[str, Any]]], Optional[str]]:
    """Execute a SQL query using Supabase RPC.
    
    This function executes a SQL query through a secured RPC endpoint.
    
    Args:
        sql_query (str): The SQL query to execute
        
    Returns:
        Tuple[Optional[List[Dict[str, Any]]], Optional[str]]: Tuple containing (data, error)
        where data is the query results if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        response = supabase.rpc('run_agent_sql_query', {'query': sql_query}).execute()
        
        # Check if there was an error in the response
        if hasattr(response, 'error') and response.error is not None:
            return None, response.error.message
        
        # Check if data is available
        if not hasattr(response, 'data'):
            return None, "No data available from SQL query"
        
        return response.data, None
        
    except Exception as e:
        error_msg = f"Error executing SQL query: {str(e)}"
        print(error_msg)
        return None, error_msg

def check_column_type_exists(table_id: str, type_id: int) -> Tuple[Optional[int], Optional[str]]:
    """Check if a column with a specific type_id exists in the table and return its ID.
    
    Args:
        table_id (str): The ID of the table to check
        type_id (int): The type_id to look for
        
    Returns:
        Tuple[Optional[int], Optional[str]]: Tuple containing (column_id, error)
        where column_id is the ID of the column if it exists, None if it doesn't exist or failed
        and error is the error message if failed, None if successful
    """
    try:
        response = supabase.table('columns').select('id').eq('table_id', table_id).eq('service_id', type_id).execute()
        
        # Check for errors
        if hasattr(response, 'error') and response.error is not None:
            return None, response.error.message
        
        # Check if data is available and has results
        if hasattr(response, 'data') and response.data:
            if len(response.data) > 0:
                return response.data[0]['id'], None
        
        return None, None  # Column doesn't exist, but no error
            
    except Exception as e:
        error_msg = f"Error checking column type: {str(e)}"
        print(error_msg)
        return None, error_msg

def create_linkedin_search_column(table_id: str, column_name: str, filters: List[Dict[str, Any]]) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
    """Create a LinkedIn search column with type_id 25.
    
    Args:
        table_id (str): The ID of the table to create the column in
        column_name (str): The name for the new column
        filters (List[Dict[str, Any]]): The filters to use for the LinkedIn search
        
    Returns:
        Tuple[Optional[Dict], Optional[str]]: Tuple containing (data, error)
        where data is the response if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        payload = {
            "p_table_id": table_id,
            "p_name": column_name,
            "p_service_id": 25,
            "p_settings": {
                "inputs": [],
                "providers": [],
                "parameters": [
                    {"description": ""},
                    {"filters": filters}
                ]
            }
        }
        
        response = supabase.rpc('upsert_smart_column', payload).execute()
        
        # Check for errors
        if hasattr(response, 'error') and response.error is not None:
            return None, response.error.message
        
        # Check if data is available
        if not hasattr(response, 'data'):
            return None, "Failed to create LinkedIn search column"
        
        # Send real-time notification for column creation
        send_realtime_notification(table_id, RealtimeEvent.COLUMN_INSERT, response.data)
        
        return response.data, None
            
    except Exception as e:
        error_msg = f"Error creating LinkedIn search column: {str(e)}"
        print(error_msg)
        return None, error_msg

def get_or_create_linkedin_search_column(table_id: str, column_name: str, filters: List[Dict[str, Any]]) -> Tuple[Optional[int], Optional[str]]:
    """Get existing LinkedIn search column ID or create a new one and return its ID.
    
    Args:
        table_id (str): The ID of the table
        column_name (str): The name for the column (used only if creating)
        filters (List[Dict[str, Any]]): The filters to use for the LinkedIn search (used only if creating)
        
    Returns:
        Tuple[Optional[int], Optional[str]]: Tuple containing (column_id, error)
        where column_id is the ID of the LinkedIn search column, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        # First check if column exists
        column_id, check_error = check_column_type_exists(table_id, 25)
        if check_error:
            return None, f"Error checking for existing LinkedIn search column: {check_error}"
        
        if column_id:
            # Column exists, return its ID
            return column_id, None
        
        # Column doesn't exist, create it
        create_result, create_error = create_linkedin_search_column(table_id, column_name, filters)
        if create_error:
            return None, f"Error creating LinkedIn search column: {create_error}"
        
        # Extract column ID from creation result
        if create_result and 'column' in create_result:
            column_data = create_result['column']
            if 'id' in column_data:
                return column_data['id'], None
        
        return None, "Failed to get column ID from creation result"
        
    except Exception as e:
        error_msg = f"Error getting or creating LinkedIn search column: {str(e)}"
        print(error_msg)
        return None, error_msg

def get_table_row_count(table_id: str) -> Tuple[Optional[int], Optional[str]]:
    """Get the number of rows in a table.
    
    Args:
        table_id (str): The ID of the table to count rows for
        
    Returns:
        Tuple[Optional[int], Optional[str]]: Tuple containing (row_count, error)
        where row_count is the number of rows if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        response = supabase.from_('rows').select('table_id').eq('table_id', table_id).execute()
        
        # Check for errors
        if hasattr(response, 'error') and response.error is not None:
            return None, response.error.message
        
        # Check if data is available and count rows
        if hasattr(response, 'data') and response.data:
            return len(response.data), None
        
        return 0, None
            
    except Exception as e:
        error_msg = f"Error counting table rows: {str(e)}"
        print(error_msg)
        return None, error_msg

def create_table_row(table_id: str, index: int) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
    """Create a new row in a table.
    
    Args:
        table_id (str): The ID of the table to create the row in
        index (int): The index position for the new row
        
    Returns:
        Tuple[Optional[Dict], Optional[str]]: Tuple containing (data, error)
        where data is the response if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        payload = {
            "p_data": {
                "table_id": table_id,
                "index": index,
                "is_visible": True
            }
        }
        
        response = supabase.rpc('create_row', payload).execute()
        
        # Check for errors
        if hasattr(response, 'error') and response.error is not None:
            return None, response.error.message
        
        # Check if data is available
        if not hasattr(response, 'data'):
            return None, "Failed to create table row"
        
        # Send real-time notification for row creation
        send_realtime_notification(table_id, RealtimeEvent.ROW_INSERT, response.data)
        
        return response.data, None
            
    except Exception as e:
        error_msg = f"Error creating table row: {str(e)}"
        print(error_msg)
        return None, error_msg

def get_table_organization_id(table_id: str) -> Tuple[Optional[str], Optional[str]]:
    """Get the organization_id for a specific table.
    
    Args:
        table_id (str): The ID of the table
        
    Returns:
        Tuple[Optional[str], Optional[str]]: Tuple containing (organization_id, error)
        where organization_id is the organization ID if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        response = supabase.table('tables').select('organization_id').eq('id', table_id).single().execute()
        
        # Check for errors
        if hasattr(response, 'error') and response.error is not None:
            return None, response.error.message
        
        # Check if data is available
        if hasattr(response, 'data') and response.data and 'organization_id' in response.data:
            return response.data['organization_id'], None
        
        return None, "Organization ID not found for this table"
            
    except Exception as e:
        error_msg = f"Error getting organization ID: {str(e)}"
        print(error_msg)
        return None, error_msg

def save_profiles_to_cells(table_id: str, profiles: List[Dict[str, Any]], linkedin_search_column_id: int) -> Tuple[bool, Optional[str]]:
    """Save LinkedIn profiles to table cells using synchronous operations.
    
    Args:
        table_id (str): The ID of the table
        profiles (List[Dict]): List of LinkedIn profiles from the API response
        linkedin_search_column_id (int): The ID of the LinkedIn search column
        
    Returns:
        Tuple[bool, Optional[str]]: Tuple containing (success, error)
        where success is True if all profiles were saved successfully, False if failed
        and error is the error message if failed, None if successful
    """
    try:
        import requests
        import uuid
        import mimetypes
        import tempfile
        import os
        from pathlib import Path
        
        # Get organization_id for the table
        organization_id, org_error = get_table_organization_id(table_id)
        if org_error:
            return False, f"Error getting organization ID: {org_error}"
        
        for index, profile in enumerate(profiles):
            row_id = index + 1  # 1-based indexing
            
            # Handle avatar URL by downloading and uploading image (synchronous version)
            avatar_url = None
            if profile.get('profile_picture_url'):
                try:
                    # Download the image
                    image_response = requests.get(profile['profile_picture_url'], timeout=30.0)
                    image_response.raise_for_status()
                    image_data = image_response.content
                    
                    # Determine MIME type and extension
                    content_type = image_response.headers.get("content-type", "")
                    extension = mimetypes.guess_extension(content_type) or ""
                    
                    # If no valid extension, infer from URL
                    if not extension:
                        url_path = Path(profile['profile_picture_url'].split("?")[0])
                        extension = url_path.suffix if url_path.suffix else ".jpg"
                        content_type = "image/jpeg" if extension == ".jpg" else content_type
                    
                    # Generate filename and path
                    filename = f"{uuid.uuid4()}{extension}"
                    full_path = f"{table_id}/{filename}"
                    
                    # Upload to Supabase storage using synchronous operation
                    upload_response = supabase.storage.from_(organization_id).upload(
                        path=full_path,
                        file=image_data,
                        file_options={
                            "content-type": content_type or "application/octet-stream",
                            "cache-control": "3600",
                            "upsert": "true",
                        },
                    )
                    
                    avatar_url = upload_response.path
                    print(f"Successfully uploaded profile image: {avatar_url}")
                    
                except Exception as img_error:
                    print(f"Warning: Failed to download/upload profile image for {profile.get('name', 'Unknown')}: {str(img_error)}")
                    # Continue without avatar if image fails
            
            # Prepare cell data
            cell_data = {
                "table_id": table_id,
                "column_id": linkedin_search_column_id,
                "row_id": row_id,
                "value": profile.get('name', 'Unknown'),
                "run_status": {"run": "completed", "message": "Profile loaded from LinkedIn search"},
                "extras": {"avatar_url": avatar_url} if avatar_url else None,
                "updated_by": None
            }
            
            # Upsert cell data using direct table operation
            cell_response = supabase.table('cells').upsert(
                cell_data,
                on_conflict="table_id,column_id,row_id"
            ).execute()
            
            # Check for errors in cell upsert
            if hasattr(cell_response, 'error') and cell_response.error is not None:
                return False, f"Error upserting cell for row {row_id}: {cell_response.error.message}"
            
            # Prepare cell details data
            cell_details_data = {
                "table_id": table_id,
                "column_id": linkedin_search_column_id,
                "row_id": row_id,
                "value": profile  # Store the entire profile object
            }
            
            # Upsert cell details using direct table operation
            details_response = supabase.table('cell_details').upsert(
                cell_details_data,
                on_conflict="table_id,column_id,row_id"
            ).execute()
            
            # Check for errors in cell details upsert
            if hasattr(details_response, 'error') and details_response.error is not None:
                return False, f"Error upserting cell details for row {row_id}: {details_response.error.message}"
            
            # Send realtime notification for cell update with complete cell data structure
            send_realtime_notification(
                table_id,
                RealtimeEvent.CELL_UPDATE,
                cell_data  # Send the complete cell data structure
            )
        
        return True, None
        
    except Exception as e:
        error_msg = f"Error saving profiles to cells: {str(e)}"
        print(error_msg)
        return False, error_msg

def poll_cell_until_complete(table_id: str, row_id: int, column_id: int) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
    """Poll a cell until its run status is 'completed' or 'error' and return the data.
    
    This function checks a specific cell's run status and returns the cell data
    once the status changes to either 'completed' or 'error'.
    
    Args:
        table_id (str): The ID of the table
        row_id (int): The ID of the row
        column_id (int): The ID of the column
        
    Returns:
        Tuple[Optional[Dict[str, Any]], Optional[str]]: Tuple containing (cell_data, error)
        where cell_data is the complete cell information if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        response = (
            supabase.table("cells")
            .select("value, run_status->>run, cell_details!fk_cell_details_table_id_column_id_row_id(value)")
            .eq("table_id", table_id)
            .eq("row_id", row_id)
            .eq("column_id", column_id)
            .execute()
        )
        
        # Check for errors in the response
        if hasattr(response, 'error') and response.error is not None:
            return None, response.error.message
        
        # Check if data is available
        if not hasattr(response, 'data') or not response.data:
            return None, f"No cell data found for table_id: {table_id}, row_id: {row_id}, column_id: {column_id}"
        
        cell_data = response.data[0]  # Get the first (and should be only) result
        run_status = cell_data.get('run')
        
        # Check if the run status is a final status (completed, failed, awaiting_input, or error)
        if run_status in ['completed', 'failed', 'awaiting_input', 'invalid_input']:
            return cell_data, None
        else:
            # Return None to indicate polling should continue for non-final statuses
            return None, None
        
    except Exception as e:
        error_msg = f"Error polling cell status: {str(e)}"
        print(error_msg)
        return None, error_msg

def check_ai_list_build_columns(table_id: str) -> Tuple[Optional[Dict[str, int]], Optional[str]]:
    """Check if columns with AI_List_Build source already exist in the table.
    
    Args:
        table_id (str): The ID of the table to check
        
    Returns:
        Tuple[Optional[Dict[str, int]], Optional[str]]: Tuple containing (column_ids_dict, error)
        where column_ids_dict maps column names to their IDs if found, None if not found or failed
        and error is the error message if failed, None if successful
    """
    try:
        # Query columns table for columns with AI_List_Build source
        response = supabase.table('columns').select('id,name,settings').eq('table_id', table_id).execute()
        
        if hasattr(response, 'error') and response.error is not None:
            return None, f"Error checking for existing columns: {response.error.message}"
        
        if not hasattr(response, 'data'):
            return None, "No data available from columns table"
        
        # Look for columns with AI_List_Build source
        column_ids = {}
        for column in response.data:
            settings = column.get('settings', {})
            if isinstance(settings, dict) and settings.get('source') == 'AI_List_Build':
                column_ids[column['name']] = column['id']
        
        return column_ids if column_ids else None, None
        
    except Exception as e:
        error_msg = f"Error checking for AI_List_Build columns: {str(e)}"
        print(error_msg)
        return None, error_msg

def create_text_column(table_id: str, column_name: str, settings: Optional[Dict[str, Any]] = None) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
    """Create a text column using agent_create_column RPC.
    
    Args:
        table_id (str): The ID of the table to create the column in
        column_name (str): The name for the new column
        settings (Optional[Dict[str, Any]]): Optional settings to include with the column
        
    Returns:
        Tuple[Optional[Dict], Optional[str]]: Tuple containing (data, error)
        where data is the response if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        # Get current column count to determine the next index
        existing_columns, columns_error = get_table_columns_info(table_id)
        if columns_error:
            return None, f"Error getting existing columns count: {columns_error}"
        
        # Calculate the next index (1-based)
        next_index = len(existing_columns) + 1 if existing_columns else 1
        
        # Prepare RPC parameters
        rpc_params = {
            'p_name': column_name,
            'p_settings': settings or {},
            'p_table_id': table_id,
            'p_type_id': 1,  # Type ID 1 for text columns
            'p_index': next_index  # Add the index parameter
        }
        
        print(f"[DEBUG] Creating text column with RPC params: {rpc_params}")
        
        # Call the agent_create_column RPC
        response = supabase.rpc('agent_create_column', rpc_params).execute()
        
        print(f"[DEBUG] Column creation response: {response}")
        
        # Check for errors
        if hasattr(response, 'error') and response.error is not None:
            error_msg = response.error.message if hasattr(response.error, 'message') else str(response.error)
            print(f"[DEBUG] Column creation error: {error_msg}")
            return None, f"Error creating text column: {error_msg}"
        
        # Check if data is available
        if not hasattr(response, 'data') or not response.data:
            return None, "Failed to create text column - no response data"
        
        print(f"[DEBUG] Column creation successful: {response.data}")
        
        # Format the response data to match expected structure
        # The RPC should return the column data directly
        if isinstance(response.data, list) and len(response.data) > 0:
            column_data = response.data[0]
        else:
            column_data = response.data
            
        formatted_response = {
            "column": column_data
        }
        
        print(f"[DEBUG] Formatted response for realtime notification: {formatted_response}")
        
        # Send real-time notification
        send_realtime_notification(table_id, RealtimeEvent.COLUMN_INSERT, formatted_response)
        
        return formatted_response, None
            
    except Exception as e:
        error_msg = f"Error creating text column: {str(e)}"
        print(error_msg)
        return None, error_msg


def paste_cells_data(table_id: str, cells_data: List[Dict[str, Any]]) -> Tuple[bool, Optional[str]]:
    """Paste cell data using the paste_cells RPC, one row at a time.
    
    Args:
        table_id (str): The ID of the table
        cells_data (List[Dict]): List of cell data to paste, each containing row_id, column_id, value, and table_id
        
    Returns:
        Tuple[bool, Optional[str]]: Tuple containing (success, error)
        where success is True if data was pasted successfully, False if failed
        and error is the error message if failed, None if successful
    """
    try:
        # Group cells by row first
        rows_data = {}
        for cell_data in cells_data:
            row_id = cell_data['row_id']
            if row_id not in rows_data:
                rows_data[row_id] = []
            rows_data[row_id].append(cell_data)
        
        print(f"[DEBUG] Processing {len(rows_data)} rows with {len(cells_data)} total cells, one row at a time")
        
        successful_rows = 0
        failed_rows = 0
        errors = []
        
        # Process each row individually
        for row_id in sorted(rows_data.keys()):  # Sort to maintain order
            row_cells = rows_data[row_id]
            
            try:
                # Format payload for single row: {"p_data": [[row_cells]]}
                payload = {
                    "p_data": [row_cells]  # Single row in array
                }
                
                print(f"[DEBUG] Pasting row {row_id} with {len(row_cells)} cells")
                
                response = supabase.rpc('paste_cells', payload).execute()
                
                # Check for errors
                if hasattr(response, 'error') and response.error is not None:
                    error_msg = response.error.message if hasattr(response.error, 'message') else str(response.error)
                    print(f"[DEBUG] paste_cells RPC failed for row {row_id}: {error_msg}")
                    
                    # Try fallback method for this row
                    fallback_success = False
                    try:
                        # Prepare all cells for this row for batch upsert
                        row_cell_data = []
                        for cell in row_cells:
                            row_cell_data.append({
                                'table_id': cell['table_id'],
                                'row_id': cell['row_id'],
                                'column_id': cell['column_id'],
                                'value': cell['value'],
                                # 'run_status': {'run': 'completed', 'message': 'Data loaded from LinkedIn search'},
                                # 'updated_by': None
                            })
                        
                        # Batch upsert all cells for this row at once
                        row_response = supabase.table('cells').upsert(
                            row_cell_data,
                            on_conflict='table_id,row_id,column_id'
                        ).execute()
                        
                        if hasattr(row_response, 'error') and row_response.error is not None:
                            print(f"[DEBUG] Fallback also failed for row {row_id}: {row_response.error}")
                        else:
                            fallback_success = True
                            print(f"[DEBUG] Fallback successful for row {row_id}")
                            
                    except Exception as fallback_e:
                        print(f"[DEBUG] Fallback exception for row {row_id}: {str(fallback_e)}")
                    
                    if not fallback_success:
                        failed_rows += 1
                        errors.append(f"Row {row_id}: {error_msg}")
                        continue
                    else:
                        successful_rows += 1
                        # Send individual cell update notifications for successful fallback
                        for cell in row_cells:
                            cell_notification_data = {
                                'table_id': cell['table_id'],
                                'row_id': cell['row_id'],
                                'column_id': cell['column_id'],
                                'value': cell['value'],
                                'run_status': {'run': 'completed', 'message': 'Data loaded from LinkedIn search'},
                                'updated_by': None
                            }
                            send_realtime_notification(
                                table_id,
                                RealtimeEvent.CELL_UPDATE,
                                cell_notification_data
                            )
                else:
                    # RPC succeeded
                    successful_rows += 1
                    print(f"[DEBUG] Successfully pasted row {row_id} via RPC")
                    
                    # Send individual cell update notifications for successful RPC
                    for cell in row_cells:
                        cell_notification_data = {
                            'table_id': cell['table_id'],
                            'row_id': cell['row_id'],
                            'column_id': cell['column_id'],
                            'value': cell['value'],
                            'run_status': {'run': 'completed', 'message': 'Data loaded from LinkedIn search'},
                            'updated_by': None
                        }
                        send_realtime_notification(
                            table_id,
                            RealtimeEvent.CELL_UPDATE,
                            cell_notification_data
                        )
                    
            except Exception as row_e:
                print(f"[DEBUG] Exception processing row {row_id}: {str(row_e)}")
                failed_rows += 1
                errors.append(f"Row {row_id}: {str(row_e)}")
                continue
        
        print(f"[DEBUG] Completed pasting: {successful_rows} successful, {failed_rows} failed out of {len(rows_data)} total rows")
        
        if successful_rows > 0:
            if failed_rows > 0:
                error_summary = "; ".join(errors[:3])  # Show first 3 errors
                if len(errors) > 3:
                    error_summary += f" (and {len(errors) - 3} more)"
                return True, f"Partial success: {successful_rows}/{len(rows_data)} rows saved. Errors: {error_summary}"
            else:
                return True, None
        else:
            error_summary = "; ".join(errors[:5])  # Show first 5 errors
            return False, f"All rows failed. Errors: {error_summary}"
            
    except Exception as e:
        error_msg = f"Error pasting cells data: {str(e)}"
        print(error_msg)
        return False, error_msg


def create_linkedin_profile_columns(table_id: str) -> Tuple[Optional[Dict[str, int]], Optional[str]]:
    """Create the four LinkedIn profile columns (Full name, Job Title, Company name, LinkedIn URL).
    If columns with AI_List_Build source already exist, they will be reused instead of creating new ones.
    
    Args:
        table_id (str): The ID of the table to create columns in
        
    Returns:
        Tuple[Optional[Dict[str, int]], Optional[str]]: Tuple containing (column_ids_dict, error)
        where column_ids_dict maps column names to their IDs if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        column_names = ["Full Name", "Job Title", "Company Name", "LinkedIn URL"]
        column_ids = {}
        
        # First check if columns with AI_List_Build source already exist
        existing_columns, check_error = check_ai_list_build_columns(table_id)
        if check_error:
            return None, f"Error checking for existing columns: {check_error}"
        
        # If we found existing columns, use them
        if existing_columns:
            print(f"[DEBUG] Found existing AI_List_Build columns: {existing_columns}")
            # Verify we have all required columns
            missing_columns = [name for name in column_names if name not in existing_columns]
            if not missing_columns:
                print(f"[DEBUG] All required columns already exist")
                return existing_columns, None
            else:
                print(f"[DEBUG] Some columns missing, will create: {missing_columns}")
                # Only create the missing columns
                column_names = missing_columns
        
        # Create any missing columns
        for column_name in column_names:
            # Create column with AI_List_Build source
            result, error = create_text_column(
                table_id, 
                column_name,
                settings={"source": "AI_List_Build"}
            )
            if error:
                return None, f"Error creating {column_name} column: {error}"
            
            # Extract column ID from response
            column_id = None
            if result:
                if 'column' in result and 'id' in result['column']:
                    column_id = result['column']['id']
                elif 'id' in result:
                    # Fallback in case the structure is different
                    column_id = result['id']
                    
            if column_id:
                column_ids[column_name] = column_id
                print(f"[DEBUG] Created {column_name} column with ID: {column_id}")
            else:
                print(f"[DEBUG] Failed to extract column ID from response: {result}")
                return None, f"Failed to get column ID for {column_name}"
        
        # Merge with existing columns if any
        if existing_columns:
            column_ids.update(existing_columns)
        
        print(f"[DEBUG] Successfully created/retrieved all LinkedIn profile columns: {column_ids}")
        return column_ids, None
        
    except Exception as e:
        error_msg = f"Error creating LinkedIn profile columns: {str(e)}"
        print(error_msg)
        return None, error_msg


def save_linkedin_profiles_to_separate_columns(table_id: str, profiles: List[Dict[str, Any]], column_ids: Dict[str, int]) -> Tuple[bool, Optional[str]]:
    """Save LinkedIn profiles to separate columns (Full name, Job Title, Company name, LinkedIn URL).
    
    Args:
        table_id (str): The ID of the table
        profiles (List[Dict]): List of LinkedIn profiles from the API response
        column_ids (Dict[str, int]): Dictionary mapping column names to their IDs
        
    Returns:
        Tuple[bool, Optional[str]]: Tuple containing (success, error)
        where success is True if all profiles were saved successfully, False if failed
        and error is the error message if failed, None if successful
    """
    try:
        cells_data = []
        
        for index, profile in enumerate(profiles):
            row_id = index + 1  # 1-based indexing
            
            # Extract data for each field
            full_name = profile.get('name', '')
            job_title = profile.get('default_position_title', '')
            company_name = ''
            if profile.get('employer') and len(profile['employer']) > 0:
                company_name = profile['employer'][0].get('company_name', '')
            linkedin_url = profile.get('linkedin_profile_url', '')
            
            # Create cell data for each column
            fields_data = {
                "Full Name": full_name,
                "Job Title": job_title,  
                "Company Name": company_name,
                "LinkedIn URL": linkedin_url
            }
            
            for field_name, field_value in fields_data.items():
                if field_name in column_ids:
                    cell_data = {
                        "row_id": row_id,
                        "column_id": column_ids[field_name],
                        "value": field_value,
                        "table_id": table_id
                    }
                    cells_data.append(cell_data)
        
        # Paste all cell data at once
        success, error = paste_cells_data(table_id, cells_data)
        if not success:
            return False, f"Error pasting LinkedIn profile data: {error}"
        
        return True, None
        
    except Exception as e:
        error_msg = f"Error saving LinkedIn profiles to separate columns: {str(e)}"
        print(error_msg)
        return False, error_msg
    

def get_column_data(table_id: str, column_id: int, max_rows: int = 50) -> Tuple[Optional[List[Dict[str, Any]]], Optional[str]]:
    """Fetch sample data for a specific column to use for description generation.
    
    Args:
        table_id (str): The ID of the table
        column_id (int): The ID of the column to get data for
        max_rows (int): Maximum number of rows to fetch for analysis
        
    Returns:
        Tuple[Optional[List[Dict[str, Any]]], Optional[str]]: Tuple containing (column_data, error)
        where column_data contains sample values from the column if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        # Get table data for this specific column
        table_data, error = get_table_data(
            table_id=table_id,
            max_rows=max_rows,
            column_ids=[column_id]
        )
        
        if error:
            return None, f"Error fetching column data: {error}"
        
        if not table_data or 'columns' not in table_data:
            return None, "No column data found"
        
        # Extract the column information
        columns = table_data.get('columns', [])
        target_column = next((col for col in columns if col.get('column_id') == column_id), None)
        
        if not target_column:
            return None, f"Column with ID {column_id} not found in response"
        
        # Extract cell values for analysis
        cells = target_column.get('cells', [])
        column_values = []
        
        for cell in cells:
            if cell.get('value') and str(cell['value']).strip():  # Skip empty values
                column_values.append({
                    'value': cell['value'],
                    'row_id': cell.get('row_id'),
                })
        
        result = {
            'column_name': target_column.get('column_name', 'Unknown'),
            'column_id': column_id,
            'sample_values': column_values,
            'total_samples': len(column_values)
        }
        
        return result, None
        
    except Exception as e:
        error_msg = f"Error fetching column data: {str(e)}"
        print(error_msg)
        return None, error_msg


def update_column_description(column_id: int, description: str) -> Tuple[bool, Optional[str]]:
    """Update the description for a specific column.
    
    Args:
        column_id (int): The ID of the column to update
        description (str): The new description to set
        
    Returns:
        Tuple[bool, Optional[str]]: Tuple containing (success, error)
        where success is True if update was successful, False if failed
        and error is the error message if failed, None if successful
    """
    try:
        response = supabase.table('columns').update({
            'description': description
        }).eq('id', column_id).execute()
        
        if hasattr(response, 'error') and response.error is not None:
            return False, f"Database error: {response.error.message}"
        
        if not hasattr(response, 'data'):
            return False, "Update response missing data"
        
        print(f"Successfully updated description for column {column_id}")
        return True, None
        
    except Exception as e:
        error_msg = f"Error updating column description: {str(e)}"
        print(error_msg)
        return False, error_msg


