

from langchain_core.messages import ToolMessage, AIMessage
from langgraph.types import interrupt

from src.outbond_ai_assistant_v3.tools.tools import tools_by_name
from src.outbond_ai_assistant_v3.state import Agent<PERSON>tate


def tool_node(state: AgentState):
    """Node that executes tools based on the model's tool calls."""
    outputs = []
    executed_tools = []
    
    # Get the last message which should contain tool calls
    last_message = state["messages"][-1]
    
    # Process each tool call - only AIMessage has tool_calls
    if isinstance(last_message, AIMessage) and last_message.tool_calls:
        for tool_call in last_message.tool_calls:
            # Check if this is a run_column tool call that needs confirmation
            if tool_call["name"] == "run_column":
                # Extract parameters for confirmation message
                args = tool_call["args"]
                column_name = args.get("column_name", f"Column {args.get('column_id', 'Unknown')}")
                count = args.get("count", 1)
                rows_text = f"{count} row" if count == 1 else f"{count} rows"
                
                confirmation_message = {
                    "column_name": column_name,
                    "column_id": args.get("column_id"),
                    "rows_count": count,
                    "message": f"Do you want to run the column '{column_name}' for {rows_text}?"
                }
                
                # Request user confirmation
                user_response = interrupt(confirmation_message)
                
                # Check if user confirmed or cancelled
                if not (user_response and str(user_response).lower() in ['yes', 'y', 'confirm', 'proceed', 'ok', 'run']):
                    # User cancelled - create error message
                    outputs.append(
                        ToolMessage(
                            content=f"Column execution cancelled by user. Column '{column_name}' was not run.",
                            name=tool_call["name"],
                            tool_call_id=tool_call["id"],
                        )
                    )
                    continue  # Skip to next tool call without executing
            
            # Execute the tool (either non-run_column tools or approved run_column)
            try:
                # Get the tool by name
                tool = tools_by_name[tool_call["name"]]
                
                # Execute the tool with the provided arguments
                tool_result = tool.invoke(tool_call["args"])
                
                # Create a tool message with the result
                outputs.append(
                    ToolMessage(
                        content=str(tool_result),  # Convert to string to ensure JSON serialization
                        name=tool_call["name"],
                        tool_call_id=tool_call["id"],
                    )
                )
            except Exception as e:
                # Handle any errors that occur during tool invocation
                error_message = f"Error executing tool '{tool_call['name']}': {str(e)}"
                
                # Create a tool message with the error
                outputs.append(
                    ToolMessage(
                        content=error_message,
                        name=tool_call["name"],
                        tool_call_id=tool_call["id"],
                    )
                )
            
            # Track executed tool
            executed_tools.append({
                "name": tool_call["name"],
                "id": tool_call["id"],
                "status": "completed"  # or "failed" if error
            })
    
    # Return the tool outputs to be added to messages
    return {
        "messages": outputs,
        "tool_calls": executed_tools
    }
