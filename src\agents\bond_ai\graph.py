"""Define a ReAct agent using LangGraph.

This agent follows the ReAct pattern (Reasoning and Acting) to solve tasks
by thinking step by step and using tools when needed.
"""


import logging
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END

from langchain_core.messages import AIMessage, HumanMessage, SystemMessage, BaseMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from pydantic import BaseModel

from bond_ai.nodes import table_indexing_node, planner_agent
from bond_ai.registry.registry import AGENT_TOOLS_MAP, AgentName
from bond_ai.prompts_v1 import (
    SUPERVISOR_AGENT_PROMPT,
    RESEARCH_AGENT_PROMPT,
    ENRICHMENT_AGENT_PROMPT,
    CONTENT_AGENT_PROMPT,
    DATA_MANAGEMENT_AGENT_PROMPT,
    EXECUTION_AGENT_PROMPT
)
from bond_ai.configuration import Configuration
from bond_ai.state import  BondAIAgentState
from bond_ai.registry import supervisor_agent_registry
from bond_ai.utils import load_chat_model

from langgraph.prebuilt import create_react_agent

load_dotenv()

# Import and register tools
from langchain_community.tools.tavily_search import TavilySearchResults
from langchain_experimental.tools import PythonREPLTool


#                                MOVE TO SDK
# ============================================================================
# UTILITY FUNCTIONS FOR EASY AGENT MANAGEMENT
# ============================================================================

def add_agent(name: str, system_prompt: str, tools: list, description: str, enabled: bool = True):
    """Add a new ReAct agent to the registry."""
    supervisor_agent_registry.register_agent(name, system_prompt, tools,description, enabled)
    print(f"✓ Added ReAct agent: {name}")

def add_node(name: str, node_function, description: str, enabled: bool = True, **kwargs):
    """Add a pre-built node function to the registry."""
    supervisor_agent_registry.register_node_prebuild(name, node_function, description,enabled, **kwargs)
    logging.info(f"✓ Added custom node: {name}")

def enable_agent(name: str):
    """Enable an agent or node."""
    supervisor_agent_registry.enable_agent(name)
    print(f"✓ Enabled: {name}")

def disable_agent(name: str):
    """Disable an agent or node."""
    supervisor_agent_registry.disable_agent(name)
    print(f"✓ Disabled: {name}")

def list_agents():
    """List all agents and nodes with their status."""
    print("\n=== AGENT REGISTRY ===")
    for name, config in supervisor_agent_registry.agents.items():
        status = "✓ ENABLED" if config.get("enabled", True) else "✗ DISABLED"
        agent_type = config.get("type", "unknown")

        if agent_type == "react_agent":
            tools_count = len(config.get("tools", []))
            print(f"{status} | {name} | Type: ReAct Agent | Tools: {tools_count}")
        elif agent_type == "custom_node":
            print(f"{status} | {name} | Type: Custom Node | Function: {config.get('node_function', {}).get('__name__', 'Unknown')}")
        else:
            print(f"{status} | {name} | Type: {agent_type}")
    print()

def rebuild_workflow():
    """Rebuild the workflow with current registry state."""
    global graph
    logging.info("Rebuilding workflow with current agent registry...")
    current_agents = supervisor_agent_registry.get_agent_names()
    logging.info(f"Active agents: {current_agents}")
    graph = create_workflow_from_registry()
    logging.info("✓ Workflow rebuilt successfully")
    return graph

def agent_node(state, agent, name):
    result = agent.invoke(state)
    return {
        "messages": [HumanMessage(content=result["messages"][-1].content, name=name)]
    }
#                                END MOVE TO SDK



# Register existing node functions
# try:
#     from bond_ai.nodes.table_indexing_node import table_indexing_node
#     agent_registry.register_node_prebuild(
#         name=  "TableIndexer",
#         node_function=  table_indexing_node,
#         description="Indexes table data and creates summaries for the agent"
#     )
#     logging.info("✓ Registered TableIndexer node")
# except ImportError as e:
#      logging.error(f"⚠ Could not import table_indexing_node: {e}")

try:
    supervisor_agent_registry.register_node_prebuild(
        name="Planner",
        node_function=planner_agent,
        description="Creates comprehensive plans for complex tasks"
    )
    logging.info("✓ Registered Planner node")
except ImportError as e:
    logging.error(f"⚠ Could not import planner_agent: {e}")

#    agent_build_prospect_list_from_db = "agent_build_prospect_list_from_db"
#     agent_linkedin = "agent_linkedin"
#     agent_run_columns_or_cells = "agent_run_columns_or_cells"

add_agent(
    name= AgentName.agent_research.value,
    system_prompt = RESEARCH_AGENT_PROMPT,
    tools= AGENT_TOOLS_MAP[AgentName.agent_research],
    description="Web research and LinkedIn discovery specialist for competitive intelligence and prospect identification"
)
add_agent(
    name= AgentName.enrichment_agent.value,
    system_prompt = ENRICHMENT_AGENT_PROMPT,
    tools= AGENT_TOOLS_MAP[AgentName.enrichment_agent],
    description="Data enrichment and contact discovery specialist for LinkedIn profiles, emails, and phone numbers"
)
add_agent(
    name= AgentName.content_agent.value,
    system_prompt = CONTENT_AGENT_PROMPT,
    tools= AGENT_TOOLS_MAP[AgentName.content_agent],
    description="AI-powered content generation specialist for personalized messaging and research insights"
)
add_agent(
    name= AgentName.data_management_agent.value,
    system_prompt = DATA_MANAGEMENT_AGENT_PROMPT,
    tools= AGENT_TOOLS_MAP[AgentName.data_management_agent],
    description="Table operations and data analysis specialist for filtering, querying, and information management"
)
add_agent(
    name= AgentName.execution_agent.value,
    system_prompt = EXECUTION_AGENT_PROMPT,
    tools= AGENT_TOOLS_MAP[AgentName.execution_agent],
    description="Column execution and monitoring specialist for batch operations and performance tracking"
)



    # 1. **search** - Web search using Tavily search engine
    #        - Parameters: query (str), max_results (int, default=5)
    #        - Returns: List of search results with URLs, titles, and content
    #        - Use case: Research current events, find general information

    #     2. **scrape_website** - Extract content from web pages
    #        - Parameters: url (str)
    #        - Returns: Clean markdown content from the webpage
    #        - Use case: Get detailed information from specific websites

    #     3. **read_table_data** - Fetch and analyze table data
    #        - Parameters: max_rows (int), filters (FilterGroup), search (str),
    #                     sorts (List[Sort]), column_ids (List[int]), summarize (bool)
    #        - Returns: Tuple of table data and summary
    #        - Use case: Read current table contents, apply filters, search data

    #     4. **upsert_linkedin_person_profile_column_from_url** - LinkedIn profile enrichment
    #        - Parameters: column_name (str), linkedin_profile_url (str), column_id (int, optional)
    #        - Returns: Success/error tuple
    #        - Use case: Create/update columns that fetch LinkedIn profile data from URLs

    #     5. **upsert_linkedin_company_profile_column_from_url** - LinkedIn company enrichment
    #        - Parameters: column_name (str), linkedin_company_url (str), column_id (int, optional)
    #        - Returns: Success/error tuple
    #        - Use case: Create/update columns that fetch LinkedIn company data from URLs

    #     6. **upsert_phone_number_column** - Phone number enrichment
    #        - Parameters: column_name (str), linkedin_profile_url (str), column_id (int, optional)
    #        - Returns: Success/error tuple
    #        - Use case: Create/update columns that find phone numbers from LinkedIn profiles

    #     7. **upsert_work_email_column** - Work email enrichment
    #        - Parameters: column_name (str), full_name (str), company_domain (str), column_id (int, optional)
    #        - Returns: Success/error tuple
    #        - Use case: Create/update columns that find work emails using name and company domain

    #     8. **upsert_text_column** - Text/formula columns
    #        - Parameters: column_name (str), text_formula (str), column_id (int, optional)
    #        - Returns: Success/error tuple
    #        - Use case: Create/update columns with static text or formulas combining other columns

    #     9. **upsert_ai_text_column** - AI-powered text generation
    #        - Parameters: column_name (str), prompt (str), required_fields (List[str]),
    #                     run_condition (str, optional), column_id (int, optional)
    #        - Returns: Success/error tuple
    #        - Use case: Create/update columns that generate AI content based on other column data

    #     10. **upsert_bond_ai_researcher_column** - AI research columns
    #         - Parameters: column_name (str), prompt (str), required_fields (List[str]),
    #                      run_condition (str, optional), column_id (int, optional)
    #         - Returns: Success/error tuple
    #         - Use case: Create/update columns that perform online research based on table data

    #     11. **upsert_ai_message_copywriter** - AI message generation
    #         - Parameters: column_name (str), prompt (str), required_fields (List[str]),
    #                      run_condition (str, optional), column_id (int, optional)
    #         - Returns: Success/error tuple
    #         - Use case: Create/update columns that generate personalized outreach messages

    #     12. **run_column** - Execute column processing
    #         - Parameters: column_id (str), column_name (str), count (int, default=1),
    #                      row_id (int, default=1), wait_results (bool, default=False)
    #         - Returns: Success/error tuple with execution results
    #         - Use case: Trigger processing of smart columns, requires user confirmation

    #     13. **search_linkedin_profiles** - LinkedIn profile search
    #         - Parameters: filters (List[LinkedInFilterType]), page (int, default=1)
    #         - Returns: Search results and profile data
    #         - Use case: Search for LinkedIn profiles using advanced filters, build prospect lists

    #     14. **read_user_view_table_filters** - Read table filters
    #         - Parameters: config (injected)
    #         - Returns: Current table filter configuration
    #         - Use case: Check current filtering settings on the table

    #     15. **update_user_view_table_filters_tool** - Update table filters
    #         - Parameters: filters (FilterGroup, optional)
    #         - Returns: Updated filter configuration
    #         - Use case: Modify how data is filtered in table views


#     agent_action_table = "agent_action_table"
#     agent_upload_csv = "agent_upload_csv"
#     agent_http_column = "agent_http_column"
#     agent_webhook_column = "agent_webhook_column"
#     agent_formula = "agent_formula"
    #agent_clean_up_column = "agent_clean_up_column"




llm = load_chat_model("bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0")


# def supervisor_agent(state):
#     """Original supervisor agent with structured output (may have compatibility issues)."""
#     # Get current team members dynamically
#     current_members = agent_registry.get_agent_names()
#     current_options = ["FINISH"] + current_members

#     # Build the prompt without template variables
#     system_content = BOND_AI_SUPERVISOR_SYSTEM_PROMPT + f"""

# <current_team>
#     <available_members>
#         {', '.join(current_members)}
#     </available_members>

#     <routing_options>
#         Valid choices: {current_options}
#     </routing_options>
# </current_team>

# <instructions>
#     Given the conversation above, who should act next?
#     Or should we FINISH? Select one of {current_options}
# </instructions>"""

#     # Create prompt manually
#     prompt = ChatPromptTemplate.from_messages([
#         ("system", system_content),
#         MessagesPlaceholder(variable_name="messages")
#     ])

#     # Update the routeResponse class with current options
#     class CurrentRouteResponse(BaseModel):
#         next: Literal[*current_options]  # type: ignore

#     supervisor_chain = prompt | llm.with_structured_output(CurrentRouteResponse)
#     return supervisor_chain.invoke(state)


def error_response_node(state):
    """Node that handles error messages and informs the user before ending."""
    from langchain_core.messages import AIMessage

    # Get the error message from state
    error_message = state.get("last_error_message", "An unexpected error occurred.")

    # Create a user-friendly error response
    user_message = f"I encountered an issue while processing your request: {error_message}\n\nPlease try rephrasing your request or contact support if the issue persists."

    # Return the error message and set next to FINISH to end the conversation
    return {
        "messages": [AIMessage(content=user_message)],
        "next": "FINISH"
    }


def task_completion_node(state):
    """Node that handles task completion reporting and updates task status."""
    from langchain_core.messages import AIMessage

    active_task_id = state.get("active_task_id")
    if not active_task_id:
        return {
            "messages": [AIMessage(content="No active task to complete.")],
            "next": "supervisor"
        }

    # Update task status to completed
    updated_state = update_task_status_in_state(state, active_task_id, "completed")

    # Generate status summary
    status_summary = get_task_status_summary(updated_state.get("plan_tasks", []))

    return {
        "messages": [AIMessage(content=f"✅ Task {active_task_id} completed successfully.\n\n{status_summary}")],
        "plan_tasks": updated_state.get("plan_tasks", []),
        "active_task_id": None,
        "next": "supervisor"
    }


def supervisor_agent_anthropic(state):
    """Enhanced supervisor agent implementation with task-aware dispatching.

    This supervisor:
    1. Checks for existing execution plans and dispatches tasks sequentially
    2. Updates task status as tasks are assigned and completed
    3. Handles task dependencies and execution order
    4. Provides clear task instructions to agents
    5. Tracks progress and handles failures gracefully
    """
    import re
    import json
    from datetime import datetime
    from langchain_core.messages import SystemMessage, AIMessage

    # Check if there's an error message in the state
    if state.get("last_error_message"):
        return {"next": "error_response"}

    # Get current team members dynamically
    current_members = supervisor_agent_registry.get_agent_names()
    current_options = ["FINISH"] + current_members

    # Check if we have an execution plan with tasks
    plan_tasks = state.get("plan_tasks", [])
    active_task_id = state.get("active_task_id")

    # If we have tasks, handle task-based dispatching
    if plan_tasks:
        return _handle_task_based_dispatching(state, plan_tasks, active_task_id, current_members)

    # If no tasks, fall back to original supervisor behavior
    return _handle_general_routing(state, current_members, current_options)


def _handle_task_based_dispatching(state, plan_tasks, active_task_id, current_members):
    """Handle task-based dispatching when execution plan exists."""
    from langchain_core.messages import AIMessage

    # Find the next task to execute
    next_task = None
    updated_tasks = []

    # Convert plan_tasks to list of dicts if they're Task objects
    tasks_list = []
    for task in plan_tasks:
        if hasattr(task, 'model_dump'):
            tasks_list.append(task.model_dump())
        elif isinstance(task, dict):
            tasks_list.append(task)
        else:
            # Try to convert to dict
            task_dict = {
                'id': getattr(task, 'id', ''),
                'order': getattr(task, 'order', 0),
                'action': getattr(task, 'action', ''),
                'agent': getattr(task, 'agent', ''),
                'tool': getattr(task, 'tool', ''),
                'why': getattr(task, 'why', ''),
                'status': getattr(task, 'status', 'pending'),
                'error': getattr(task, 'error', None)
            }
            tasks_list.append(task_dict)

    # Sort tasks by order
    tasks_list.sort(key=lambda x: x.get('order', 0))

    # Mark current active task as completed if it exists
    if active_task_id:
        for task in tasks_list:
            if task['id'] == active_task_id and task['status'] == 'in_progress':
                task['status'] = 'completed'
                logging.info(f"✓ Completed task: {task['id']} - {task['action']}")
                break

    # Find next pending task
    for task in tasks_list:
        if task['status'] == 'pending':
            # Check if this agent is available
            if task['agent'] in current_members:
                next_task = task
                break
            else:
                # Agent not available, mark as failed
                task['status'] = 'failed'
                task['error'] = f"Agent '{task['agent']}' not available"
                logging.warning(f"⚠ Task {task['id']} failed: Agent '{task['agent']}' not available")

    # If no next task found, check if all tasks are completed
    if not next_task:
        pending_tasks = [t for t in tasks_list if t['status'] == 'pending']
        failed_tasks = [t for t in tasks_list if t['status'] == 'failed']

        if not pending_tasks:
            # All tasks completed or failed
            completed_tasks = [t for t in tasks_list if t['status'] == 'completed']

            if failed_tasks:
                failure_summary = "\n".join([f"- {t['action']}: {t.get('error', 'Unknown error')}" for t in failed_tasks])
                message = f"Execution plan completed with {len(completed_tasks)} successful tasks and {len(failed_tasks)} failed tasks.\n\nFailed tasks:\n{failure_summary}"
            else:
                message = f"✅ Execution plan completed successfully! All {len(completed_tasks)} tasks have been executed."

            return {
                "messages": [AIMessage(content=message)],
                "plan_tasks": _convert_to_task_objects(tasks_list),
                "active_task_id": None,
                "next": "FINISH"
            }

    # Dispatch next task
    if next_task:
        # Mark task as in progress
        next_task['status'] = 'in_progress'

        # Create task delegation message
        delegation_message = f"""
🎯 **TASK ASSIGNMENT**

**Task ID:** {next_task['id']}
**Action:** {next_task['action']}
**Tool Required:** {next_task['tool']}
**Objective:** {next_task['why']}

**Instructions:** Please execute this task using the specified tool. Focus on the action described and provide clear results.
"""

        logging.info(f"🚀 Dispatching task {next_task['id']} to agent '{next_task['agent']}'")

        return {
            "messages": [AIMessage(content=delegation_message)],
            "plan_tasks": _convert_to_task_objects(tasks_list),
            "active_task_id": next_task['id'],
            "next": next_task['agent']
        }

    # Fallback - no executable tasks
    return {
        "messages": [AIMessage(content="No executable tasks found in the current plan.")],
        "plan_tasks": _convert_to_task_objects(tasks_list),
        "active_task_id": None,
        "next": "FINISH"
    }


def _convert_to_task_objects(tasks_list):
    """Convert list of task dicts back to Task objects."""
    from bond_ai.models.planner_model import Task

    task_objects = []
    for task_dict in tasks_list:
        try:
            task_obj = Task(**task_dict)
            task_objects.append(task_obj)
        except Exception as e:
            logging.warning(f"Failed to convert task dict to Task object: {e}")
            # Keep as dict if conversion fails
            task_objects.append(task_dict)

    return task_objects


def get_task_status_summary(plan_tasks):
    """Generate a summary of task execution status."""
    if not plan_tasks:
        return "No tasks in execution plan."

    # Convert to list of dicts if needed
    tasks_list = []
    for task in plan_tasks:
        if hasattr(task, 'model_dump'):
            tasks_list.append(task.model_dump())
        elif isinstance(task, dict):
            tasks_list.append(task)
        else:
            task_dict = {
                'id': getattr(task, 'id', ''),
                'action': getattr(task, 'action', ''),
                'status': getattr(task, 'status', 'pending'),
                'agent': getattr(task, 'agent', ''),
            }
            tasks_list.append(task_dict)

    # Count by status
    status_counts = {}
    for task in tasks_list:
        status = task.get('status', 'pending')
        status_counts[status] = status_counts.get(status, 0) + 1

    # Generate summary
    total_tasks = len(tasks_list)
    summary_parts = [f"📊 **Task Execution Summary** ({total_tasks} total tasks)"]

    status_emojis = {
        'completed': '✅',
        'in_progress': '🔄',
        'pending': '⏳',
        'failed': '❌'
    }

    for status, count in status_counts.items():
        emoji = status_emojis.get(status, '❓')
        summary_parts.append(f"{emoji} {status.title()}: {count}")

    return "\n".join(summary_parts)


def update_task_status_in_state(state, task_id, new_status, error_message=None):
    """Utility function to update task status in state."""
    plan_tasks = state.get("plan_tasks", [])
    if not plan_tasks:
        return state

    # Convert to list of dicts if needed
    tasks_list = []
    for task in plan_tasks:
        if hasattr(task, 'model_dump'):
            task_dict = task.model_dump()
        elif isinstance(task, dict):
            task_dict = task.copy()
        else:
            task_dict = {
                'id': getattr(task, 'id', ''),
                'order': getattr(task, 'order', 0),
                'action': getattr(task, 'action', ''),
                'agent': getattr(task, 'agent', ''),
                'tool': getattr(task, 'tool', ''),
                'why': getattr(task, 'why', ''),
                'status': getattr(task, 'status', 'pending'),
                'error': getattr(task, 'error', None)
            }
        tasks_list.append(task_dict)

    # Update the specific task
    for task in tasks_list:
        if task['id'] == task_id:
            task['status'] = new_status
            if error_message:
                task['error'] = error_message
            elif new_status == 'completed':
                task['error'] = None  # Clear any previous errors
            break

    # Convert back to Task objects and update state
    updated_state = state.copy()
    updated_state["plan_tasks"] = _convert_to_task_objects(tasks_list)

    return updated_state


def _handle_general_routing(state, current_members, current_options):
    """Handle general routing when no execution plan exists."""
    import re
    import json
    from datetime import datetime
    from langchain_core.messages import SystemMessage

    # Build dynamic agent directory
    agent_directory_entries = []
    react_agents = supervisor_agent_registry.get_react_agents()
    custom_nodes = supervisor_agent_registry.get_custom_nodes()

    # Process ReAct agents
    for name, config in react_agents.items():
        description = config.get('description', 'ReAct agent')
        tools = config.get('tools', [])

        # Get tool names
        tool_names = []
        for tool in tools:
            if hasattr(tool, 'name'):
                tool_names.append(tool.name)
            elif hasattr(tool, '__name__'):
                tool_names.append(tool.__name__)
            else:
                tool_names.append(str(tool))

        tools_str = ', '.join(tool_names) if tool_names else 'No specific tools'
        agent_entry = f"- **{name}**: {description} (Tools: {tools_str})"
        agent_directory_entries.append(agent_entry)

    # Process custom nodes
    for name, config in custom_nodes.items():
        description = config.get('description', 'Custom node function')
        agent_entry = f"- **{name}**: {description} (Type: Custom node function)"
        agent_directory_entries.append(agent_entry)

    # Build the agent directory section
    agent_directory = f"""**AVAILABLE SPECIALIZED AGENTS:**
{chr(10).join(agent_directory_entries)}

**ROUTING OPTIONS:**
Valid choices: {', '.join(current_options)}
• Use "FINISH" when the user's request has been completely fulfilled
• Choose the team member whose expertise best matches the current need"""

    # Get context variables (with defaults for missing values)
    table_id = getattr(state, 'table_id', 'Not available')
    today_date = datetime.now().strftime('%Y-%m-%d')
    current_filters = getattr(state, 'current_filters', 'Not available')
    table_summary = state.get('table_summary', 'Not available')
    selected_row_ids = state.get('selected_row_ids', 'None')
    selected_column_ids = state.get('selected_column_ids', 'None')
    mode = state.get('mode', 'Not available')

    # Format the supervisor prompt with context and agent directory
    system_content = SUPERVISOR_AGENT_PROMPT.format(
        table_id=table_id,
        today_date=today_date,
        current_filters=current_filters,
        table_summary=table_summary,
        selected_row_ids=selected_row_ids,
        selected_column_ids=selected_column_ids,
        mode=mode,
        agent_directory=agent_directory
    ) + f"""

**ROUTING INSTRUCTIONS:**
Based on the conversation history, determine who should act next.
Respond with a JSON object: {{"next": "AGENT_NAME"}}
where AGENT_NAME is one of: {current_options}"""

    # Create messages manually instead of using ChatPromptTemplate
    messages = [SystemMessage(content=system_content)]

    # Add the conversation messages from state
    if "messages" in state:
        messages.extend(state["messages"])

    # Invoke the LLM directly with the messages
    response = llm.invoke(messages)

    # Extract the content from the response
    if hasattr(response, 'content'):
        content = response.content
    else:
        content = str(response)

    # Handle case where content might be a list or other non-string type
    if isinstance(content, list):
        if content and isinstance(content[0], str):
            content = content[0]
        else:
            content = str(content)
    elif not isinstance(content, str):
        content = str(content)

    # Try to parse JSON from the response
    try:
        # Look for JSON in the response
        if content.strip().startswith('{') and content.strip().endswith('}'):
            parsed = json.loads(content.strip())
            next_agent = parsed.get("next")
        else:
            # Look for JSON in the response
            json_match = re.search(r'\{[^}]*"next"[^}]*\}', content)
            if json_match:
                json_str = json_match.group()
                parsed = json.loads(json_str)
                next_agent = parsed.get("next")
            else:
                next_agent = None

            if next_agent and next_agent in current_options:
                logging.info(f"Supervisor routing decision: {next_agent} (from options: {current_options})")
                logging.debug(f"LLM response content: {content}")
                return {"next": next_agent}

    except (json.JSONDecodeError, AttributeError):
        pass
        # Fallback parsing: look for any valid option in the response
    next_agent = None
    for option in current_options:
        if option.upper() in content.upper():
                next_agent = option
                break

         # Default fallback
    if next_agent is None or next_agent not in current_options:
        next_agent = "FINISH"

    logging.info(f"Supervisor routing decision: {next_agent} (from options: {current_options})")
    logging.debug(f"LLM response content: {content}")

    # Return in the same format as the original function
    return {"next": next_agent}

#MOVE TO SDK
def _create_react_agent(name: str, system_prompt: str, tools: list, llm=None):
    """Factory function to create a ReAct agent with system prompt and tools."""
    if llm is None:
        llm = ChatOpenAI(model="gpt-4o-mini")

    prompt = ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        MessagesPlaceholder(variable_name="messages")
    ])

    agent = create_react_agent(
        model=llm, 
        tools=tools, 
        prompt=prompt)
    return agent
    #return functools.partial(agent_node, agent=agent, name=name)


def create_workflow_from_registry():
    """Create workflow using the agent registry."""
    workflow = StateGraph(BondAIAgentState)
    
    workflow.add_node("table_indexing", table_indexing_node)
    workflow.add_edge("table_indexing", "supervisor")

    # Create ReAct agents from registry
    react_agents = supervisor_agent_registry.get_react_agents()
    for name, config in react_agents.items():
        # Filter config to only include parameters that _create_react_agent expects
        agent_params = {
            "name": name,
            "system_prompt": config["system_prompt"],
            "tools": config["tools"]
        }
        # Add llm if specified in config
        if "llm" in config:
            agent_params["llm"] = config["llm"]

        node = _create_react_agent(**agent_params)
        workflow.add_node(name, node)
        logging.info(f"✓ Added ReAct agent: {name}")

    # Add custom nodes from registry
    custom_nodes = supervisor_agent_registry.get_custom_nodes()
    for name, config in custom_nodes.items():
        node_function = config["node_function"]
        workflow.add_node(name, node_function)
        logging.info(f"✓ Added custom node: {name}")

    # Add supervisor, error response, and task completion nodes
    workflow.add_node("supervisor", supervisor_agent_anthropic)
    workflow.add_node("error_response", error_response_node)
    workflow.add_node("task_completion", task_completion_node)

    # Connect all agents/nodes to supervisor
    current_members = supervisor_agent_registry.get_agent_names()
    for member in current_members:
        workflow.add_edge(member, "supervisor")

    # Connect error response node to END
    workflow.add_edge("error_response", END)

    # Connect task completion node to supervisor
    workflow.add_edge("task_completion", "supervisor")

    # Create conditional routing
    conditional_map = {k: k for k in current_members}
    conditional_map["FINISH"] = END
    conditional_map["error_response"] = "error_response"
    conditional_map["task_completion"] = "task_completion"
    workflow.add_conditional_edges("supervisor", lambda x: x["next"], conditional_map)

    workflow.set_entry_point("table_indexing")
    return workflow.compile()



# Create the initial workflow
graph = create_workflow_from_registry()


# ============================================================================
# EXAMPLE USAGE OF THE FLEXIBLE AGENT REGISTRY
# ============================================================================

"""
Example of how to use the flexible agent registry:

# 1. Add a new ReAct agent
add_agent(
    "DataAnalyst",
    "You are a data analysis expert. Analyze data and provide insights.",
    [python_repl_tool, tavily_tool]
)

# 2. Add a pre-built node function from imports
from bond_ai.nodes.table_indexing_node import table_indexing_node
add_node("TableIndexer", table_indexing_node, description="Indexes table data and creates summaries")

# 3. Add another existing node
from bond_ai.nodes.planner_node import planner_agent
add_node("Planner", planner_agent, description="Creates plans for complex tasks")

# 4. Disable an existing agent
disable_agent("Coder")

# 5. List all agents and nodes
list_agents()

# 6. Rebuild workflow with new configuration
rebuild_workflow()

# 7. Add a custom tool and agent
from langchain_core.tools import tool

@tool
def custom_calculator(expression: str) -> str:
    \"\"\"Calculate mathematical expressions.\"\"\"
    try:
        result = eval(expression)
        return f"Result: {result}"
    except:
        return "Error in calculation"

agent_registry.register_tool("calculator", custom_calculator)

add_agent(
    "MathExpert",
    "You are a mathematics expert. Solve mathematical problems step by step.",
    [custom_calculator]
)

# 8. Create a custom node function and register it
def custom_validator_node(state, config):
    \"\"\"Custom node that validates data.\"\"\"
    # Your custom logic here
    return {"messages": [{"content": "Data validated successfully"}]}

add_node("DataValidator", custom_validator_node, description="Validates data integrity")

rebuild_workflow()
"""
