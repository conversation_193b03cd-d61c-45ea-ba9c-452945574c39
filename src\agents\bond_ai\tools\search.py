"""Search tool for web search functionality."""

from typing import Optional, Any, cast
from langchain_core.tools import tool
from langchain_community.tools.tavily_search import TavilySearchResults
from langgraph.config import get_stream_writer


@tool
def search(
    query: str,
    max_results: Optional[int] = 5,
) -> Optional[list[dict[str, Any]]]:
    """Search for general web results.

    This function performs a search using the Tavily search engine, which is designed
    to provide comprehensive, accurate, and trusted results. It's particularly useful
    for answering questions about current events.

    Args:
        query: The search query string
        max_results: Optional maximum number of results to return. 
        RECOMMENDED to start with 5 and then increase if needed.
    """
    stream_writer = get_stream_writer()
    stream_writer({"custom_tool_call": f"Searching the web for {query}"})    
    wrapped = TavilySearchResults(max_results=max_results)
    
    # Use invoke instead of ainvoke for synchronous operation
    result = wrapped.invoke({"query": query})
    return cast(list[dict[str, Any]], result)
