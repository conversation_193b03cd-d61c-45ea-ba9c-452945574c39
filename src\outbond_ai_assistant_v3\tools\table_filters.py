"""Table filters management tools."""

from typing import Optional, Dict, <PERSON>, <PERSON><PERSON>, Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool, InjectedToolArg
from langgraph.config import get_stream_writer
from agent.configuration import Configuration

from .models import FilterGroup
from ..agent_db import get_table_filters, update_table_filters


@tool
def read_user_view_table_filters(
    config: Annotated[RunnableConfig, InjectedToolArg]
) -> <PERSON>ple[Optional[Dict[str, Any]], Optional[str]]:
    """Read the filters field from the current table.
    
    This tool retrieves only the filters field from the current table,
    which contains filter configurations that determine how data is filtered
    in table views.
    
    Parameters:
        config: Configuration injected by the system which contains table_id
        
    Returns:
        Tuple[Optional[Dict[str, Any]], Optional[str]]: Tuple containing (filters_data, error)
        where filters_data is the filters field if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"Reading table filters"})
        # Get the table_id from the configuration
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        
        # Call the database function
        filters, error = get_table_filters(table_id)
        
        if error:
            return None, error
            
        # Return the filters data
        return filters, None
            
    except Exception as e:
        error_msg = f"Error reading table filters: {str(e)}"
        return None, error_msg


@tool
def update_user_view_table_filters_tool(
    config: Annotated[RunnableConfig, InjectedToolArg],
    filters: Optional[FilterGroup] = None
) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
    """Update the filters field for the current table.
    
    This tool updates only the filters field in the current table,
    which controls how data is filtered in table views.
    
    Parameters:
        config: Configuration injected by the system which contains table_id
        filters: Filtering conditions using the FilterGroup model, same structure
                as used in read_table_data tool
        
    Returns:
        Tuple[Optional[Dict[str, Any]], Optional[str]]: Tuple containing (updated_data, error)
        where updated_data is the updated table data if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"Updating table filters"})
        # Get the table_id from the configuration
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        
        # Convert filters to dict without complex formatting
        if filters:
            filters_dict = filters.model_dump(mode='json')
        else:
            filters_dict = {}
        
        # Call the database function with the filters as they are
        updated_data, error = update_table_filters(table_id, filters_dict)
        
        if error:
            return None, error
            
        # Return the updated data
        return updated_data, None
            
    except Exception as e:
        error_msg = f"Error updating table filters: {str(e)}"
        return None, error_msg
