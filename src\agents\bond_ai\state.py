"""Define the state structures for the agent."""

from __future__ import annotations

from typing import Annotated, List, Sequence, TypedDict, Optional

from langchain_core.messages import BaseMessage
from langgraph.graph.message import add_messages
from pydantic import Field

from bond_ai.models.planner_model import Task

# State reducers
def tasks_reducer(current: List[Task], new: List[Task]) -> List[Task]:
    """Reducer for tasks list - merge by ID, keeping latest version."""
    if not current:
        return new
    if not new:
        return current
    
    # Create a dict for efficient lookup
    current_dict = {task.id: task for task in current}
    
    # Update with new tasks
    for task in new:
        current_dict[task.id] = task
    
    return list(current_dict.values())

def add_tool_calls(existing: Optional[List[dict]], new: Optional[List[dict]]) -> List[dict]:
    """Reducer that accumulates tool calls instead of replacing them."""
    if existing is None:
        existing = []
    if new is None:
        new = []
    return existing + new

def preserve_table_summary(existing: Optional[str], new: Optional[str]) -> Optional[str]:
    """Preserve existing table summary unless explicitly updated."""
    return new if new is not None else existing


class BondAIAgentState(TypedDict):
    """The state of the agent.
    
    This defines the structure of data flowing through the ReAct agent.
    """
    # add_messages is a reducer that combines message lists
    # See https://langchain-ai.github.io/langgraph/concepts/low_level/#reducers
    messages: Annotated[Sequence[BaseMessage], add_messages]
    table_summary: Annotated[Optional[str], preserve_table_summary]
    mode: Optional[str]
    selected_row_ids: Optional[int]
    selected_column_ids: Optional[str]
    intents: Optional[List[str]]
    tool_calls: Annotated[Optional[List[dict]], add_tool_calls]
    
    # Planning and execution
    plan_tasks: Annotated[List[Task], tasks_reducer]
    #plan: Optional[Plan]
    active_task_id: Optional[str]   # ID of currently executing task
    
    #supervisor
    next: Optional[str]   # Next node to run
    
    #error handling
    last_error_message: Optional[str]
