"""Website scraping tool using FireCrawl."""

import os
from langchain_core.tools import tool
from firecrawl import FirecrawlApp
from langgraph.config import get_stream_writer
from dotenv import load_dotenv

load_dotenv()

FIRECRAWL_API_KEY = os.getenv("FIRECRAWL_API_KEY")


@tool
def scrape_website(url: str) -> str:
    """Scrape a website and return its content in clean, LLM-ready markdown format.
    
    This tool uses FireCrawl to extract content from a single webpage. It's useful when you need
    detailed information from a specific page. The content is returned as clean markdown,
    making it ideal for analysis.
    
    Args:
        url: The URL of the website to scrape (e.g., "https://example.com")
        
    Returns:
        The content of the website in markdown format
    """
    if not FIRECRAWL_API_KEY:
        return "Error: FIRECRAWL_API_KEY environment variable is not set."

    try:
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"Browsing website: {url}"})
        app = FirecrawlApp(api_key=FIRECRAWL_API_KEY)
        
        # Use the exact syntax from FireCrawl docs
        scrape_result = app.scrape_url(url, formats=['markdown'])
        
        # Check if we have the expected structure
        if not scrape_result:
            return "No content was found on the provided URL."
        
        # Try to access the markdown content
        try:
            # The response should be a dict with 'data' containing 'markdown'
            if isinstance(scrape_result, dict):
                markdown_content = scrape_result.get('data', {}).get('markdown', '')
            else:
                # If it's an object, try attribute access
                markdown_content = getattr(scrape_result, 'markdown', '') or getattr(scrape_result.data, 'markdown', '') if hasattr(scrape_result, 'data') else ''
            
            if not markdown_content:
                return f"No markdown content found. Response: {scrape_result}"
            
            return f"Content from {url}:\n\n{markdown_content}"
            
        except Exception as parse_error:
            return f"Error parsing response: {parse_error}. Raw response: {scrape_result}"
    
    except Exception as e:
        return f"Error scraping website: {str(e)}"
