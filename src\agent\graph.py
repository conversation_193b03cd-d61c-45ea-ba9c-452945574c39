"""Define a ReAct agent using LangGraph.

This agent follows the ReAct pattern (Reasoning and Acting) to solve tasks
by thinking step by step and using tools when needed.
"""

import json
from typing import Dict, Any

from langchain_core.messages import SystemMessage, ToolMessage
from langchain_core.runnables import RunnableConfig
from src.agent.utils import load_chat_model
from langgraph.graph import StateGraph, END
from src.agent.prompts import AI_RESEARCH_PROMPT
from agent.configuration import Configuration
from agent.state import AgentState
from agent.tools import tools, tools_by_name
from agent.utils import load_chat_model
from dotenv import load_dotenv
from datetime import datetime

import os
load_dotenv()

AI_RESEARCH_MODEL = os.getenv("AI_RESEARCH_MODEL")


def call_model(
    state: AgentState,
    config: RunnableConfig,
):
    """Node that calls the LLM to generate the next response."""
    # Get configuration
    configuration = Configuration.from_runnable_config(config)
    print("load_chat_model")
    model = load_chat_model(AI_RESEARCH_MODEL).bind_tools(tools)
    
    # Create the messages
    messages = [
        SystemMessage(AI_RESEARCH_PROMPT.format(today_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"))),
        *state["messages"]
    ]
    
    # Call the model with the current state
    response = model.invoke(messages, config)
    
    # Return the response to be added to messages
    return {"messages": [response]}


def tool_node(state: AgentState):
    """Node that executes tools based on the model's tool calls."""
    outputs = []
    
    # Get the last message which should contain tool calls
    last_message = state["messages"][-1]
    
    # Process each tool call
    for tool_call in last_message.tool_calls:
        try:
            # Get the tool by name
            tool = tools_by_name[tool_call["name"]]
            
            # Execute the tool with the provided arguments
            tool_result = tool.invoke(tool_call["args"])
            
            # Create a tool message with the result
            outputs.append(
                ToolMessage(
                    content=str(tool_result),  # Convert to string to ensure JSON serialization
                    name=tool_call["name"],
                    tool_call_id=tool_call["id"],
                )
            )
        except Exception as e:
            # Handle any errors that occur during tool invocation
            error_message = f"Error executing tool '{tool_call['name']}': {str(e)}"
            
            # Create a tool message with the error
            outputs.append(
                ToolMessage(
                    content=error_message,
                    name=tool_call["name"],
                    tool_call_id=tool_call["id"],
                )
            )
    
    # Return the tool outputs to be added to messages
    return {"messages": outputs}


def should_continue(state: AgentState):
    """Determine whether to continue with tool execution or end the conversation."""
    messages = state["messages"]
    last_message = messages[-1]
    
    # If there are tool calls, continue to the tool node
    if hasattr(last_message, "tool_calls") and last_message.tool_calls:
        return "continue"
    
    # Otherwise, end the conversation
    return "end"


# Define the graph
def create_graph():
    """Create and return the ReAct agent graph."""
    # Define a new graph with our state
    workflow = StateGraph(AgentState, config_schema=Configuration)
    
    # Add nodes to the graph
    workflow.add_node("agent", call_model)
    workflow.add_node("tools", tool_node)
    
    # Set the entry point
    workflow.set_entry_point("agent")
    
    # Add conditional edges from agent node
    workflow.add_conditional_edges(
        "agent",
        should_continue,
        {
            "continue": "tools",
            "end": END,
        },
    )
    
    # Add edge from tools back to agent
    workflow.add_edge("tools", "agent")
    
    # Compile the graph
    graph = workflow.compile()
    graph.name = "Research Agent"  # Custom name for LangSmith
    
    return graph


# Create the graph
graph = create_graph()
