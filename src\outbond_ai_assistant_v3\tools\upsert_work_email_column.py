"""Work email column creation tool."""

from typing import <PERSON><PERSON>, <PERSON><PERSON>, Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool, InjectedToolArg
from langgraph.config import get_stream_writer
from agent.configuration import Configuration
import json

from ..agent_db import upsert_smart_column


@tool
def upsert_work_email_column(
    config: Annotated[RunnableConfig, InjectedToolArg],
    column_name: str,
    full_name: str,
    company_domain: str,
    column_id: Optional[int] = None,
) -> Tuple[Optional[str], Optional[str]]:
    """Create a new work email column or update an existing one using name and company domain.
    
    IMPORTANT: ALWAYS provide the column_id if you want to update/edit an existing column. Do NOT duplicate the column by NOT providing the column_id.
    Parameters:
        config: Configuration injected by the system
        column_name: A short descriptive name of the column. Must be unique per table.
        full_name: A SINGLE injection path of the full name data point.
        company_domain: A SINGLE injection path of the company domain data point.
        column_id: Optional ID of the existing column to update/edit. If not provided, a new column will be created.
        
    Returns:
        <PERSON>ple[Optional[str], Optional[str]]: <PERSON><PERSON> containing (success_message, error_message)
        where success_message is a confirmation if successful, None if failed
        and error_message is the error description if failed, None if successful
    """
    try:
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"Working on {column_name} column"})
        # Set up metadata
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        service_id = 10
        
        inputs = [
            {"full_name": full_name},
            {"company_domain": company_domain}
        ]
        
        # Set up providers
        providers = [
            {"email_providers": ["leadmagic", "findymail", "prospeo"]},
            {"verify_providers": ["millionverifier"]}
        ]
        
        # Set up parameters (empty dict as in the payload)
        parameters = [{}]
        
        # Call the create_smart_column function
        response, error = upsert_smart_column(
            table_id=table_id,
            column_name=column_name,
            service_id=service_id,
            inputs=inputs,
            parameters=parameters,
            providers=providers,
            column_id=column_id
        )
        
        if error:
            return None, error
            
        # Extract column details from response
        column = response.get('column', {})
        column_id = column.get('id')
        column_type = column.get('type') 
        name = column.get('name')
        
        action = "updated" if column_id is not None else "created"
        
        # Create JSON response
        response_json = {
            "action": action,
            "column_name": name,
            "message": f"The work email column was successfully {action}. Column name: '{name}' Column ID: {column_id}, Column Type: {column_type}"
        }
        
        return json.dumps(response_json), None
        
    except Exception as e:
        return None, str(e)
