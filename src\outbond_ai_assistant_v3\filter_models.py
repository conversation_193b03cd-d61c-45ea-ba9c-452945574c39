"""
Pydantic models for person search filters.

These models define the structure and validation for various filters that can be applied
when searching for people. Each filter has specific types and allowed values that help
an AI agent construct valid search queries.
"""

from typing import List, Literal, Optional, Union
from pydantic import BaseModel, Field, field_validator
from enum import Enum


# Enums for predefined values
# These enums define the exact allowed values for various filter fields

class FilterTypeEnum(str, Enum):
    """Enumeration of all available filter types for person search."""
    CURRENT_COMPANY = "CURRENT_COMPANY"
    CURRENT_TITLE = "CURRENT_TITLE"
    PAST_TITLE = "PAST_TITLE"
    COMPANY_HEADQUARTERS = "COMPANY_HEADQUARTERS"
    COMPANY_HEADCOUNT = "COMPANY_HEADCOUNT"
    REGION = "REGION"
    INDUSTRY = "INDUSTRY"
    PROFILE_LANGUAGE = "PROFILE_LANGUAGE"
    SENIORITY_LEVEL = "SENIORITY_LEVEL"
    YEARS_AT_CURRENT_COMPANY = "YEARS_AT_CURRENT_COMPANY"
    YEARS_IN_CURRENT_POSITION = "YEARS_IN_CURRENT_POSITION"
    YEARS_OF_EXPERIENCE = "YEARS_OF_EXPERIENCE"
    FIRST_NAME = "FIRST_NAME"
    LAST_NAME = "LAST_NAME"
    FUNCTION = "FUNCTION"
    PAST_COMPANY = "PAST_COMPANY"
    COMPANY_TYPE = "COMPANY_TYPE"
    POSTED_ON_LINKEDIN = "POSTED_ON_LINKEDIN"
    RECENTLY_CHANGED_JOBS = "RECENTLY_CHANGED_JOBS"
    IN_THE_NEWS = "IN_THE_NEWS"
    KEYWORD = "KEYWORD"
    SCHOOL = "SCHOOL"


class CompanyHeadcountEnum(str, Enum):
    """Valid company size ranges based on number of employees."""
    SELF_EMPLOYED = "Self-employed"
    SIZE_1_10 = "1-10"
    SIZE_11_50 = "11-50"
    SIZE_51_200 = "51-200"
    SIZE_201_500 = "201-500"
    SIZE_501_1000 = "501-1,000"
    SIZE_1001_5000 = "1,001-5,000"
    SIZE_5001_10000 = "5,001-10,000"
    SIZE_10001_PLUS = "10,001+"


class ProfileLanguageEnum(str, Enum):
    """Supported languages for LinkedIn profiles."""
    ARABIC = "Arabic"
    ENGLISH = "English"
    SPANISH = "Spanish"
    PORTUGUESE = "Portuguese"
    CHINESE = "Chinese"
    FRENCH = "French"
    ITALIAN = "Italian"
    RUSSIAN = "Russian"
    GERMAN = "German"
    DUTCH = "Dutch"
    TURKISH = "Turkish"
    TAGALOG = "Tagalog"
    POLISH = "Polish"
    KOREAN = "Korean"
    JAPANESE = "Japanese"
    MALAY = "Malay"
    NORWEGIAN = "Norwegian"
    DANISH = "Danish"
    ROMANIAN = "Romanian"
    SWEDISH = "Swedish"
    BAHASA_INDONESIA = "Bahasa Indonesia"
    CZECH = "Czech"


class SeniorityLevelEnum(str, Enum):
    """Professional seniority levels."""
    OWNER_PARTNER = "Owner / Partner"
    CXO = "CXO"
    VICE_PRESIDENT = "Vice President"
    DIRECTOR = "Director"
    EXPERIENCED_MANAGER = "Experienced Manager"
    ENTRY_LEVEL_MANAGER = "Entry Level Manager"
    STRATEGIC = "Strategic"
    SENIOR = "Senior"
    ENTRY_LEVEL = "Entry Level"
    IN_TRAINING = "In Training"


class YearsRangeEnum(str, Enum):
    """Standard year ranges for experience-related filters."""
    LESS_THAN_1 = "Less than 1 year"
    ONE_TO_TWO = "1 to 2 years"
    THREE_TO_FIVE = "3 to 5 years"
    SIX_TO_TEN = "6 to 10 years"
    MORE_THAN_TEN = "More than 10 years"


class FunctionEnum(str, Enum):
    """Job functions/departments."""
    ACCOUNTING = "Accounting"
    ADMINISTRATIVE = "Administrative"
    ARTS_AND_DESIGN = "Arts and Design"
    BUSINESS_DEVELOPMENT = "Business Development"
    COMMUNITY_AND_SOCIAL_SERVICES = "Community and Social Services"
    CONSULTING = "Consulting"
    EDUCATION = "Education"
    ENGINEERING = "Engineering"
    ENTREPRENEURSHIP = "Entrepreneurship"
    FINANCE = "Finance"
    HEALTHCARE_SERVICES = "Healthcare Services"
    HUMAN_RESOURCES = "Human Resources"
    INFORMATION_TECHNOLOGY = "Information Technology"
    LEGAL = "Legal"
    MARKETING = "Marketing"
    MEDIA_AND_COMMUNICATION = "Media and Communication"
    MILITARY_AND_PROTECTIVE_SERVICES = "Military and Protective Services"
    OPERATIONS = "Operations"
    PRODUCT_MANAGEMENT = "Product Management"
    PROGRAM_AND_PROJECT_MANAGEMENT = "Program and Project Management"
    PURCHASING = "Purchasing"
    QUALITY_ASSURANCE = "Quality Assurance"
    REAL_ESTATE = "Real Estate"
    RESEARCH = "Research"
    SALES = "Sales"
    CUSTOMER_SUCCESS_AND_SUPPORT = "Customer Success and Support"


class CompanyTypeEnum(str, Enum):
    """Types of companies/organizations."""
    PUBLIC_COMPANY = "Public Company"
    PRIVATELY_HELD = "Privately Held"
    NON_PROFIT = "Non Profit"
    EDUCATIONAL_INSTITUTION = "Educational Institution"
    PARTNERSHIP = "Partnership"
    SELF_EMPLOYED = "Self Employed"
    SELF_OWNED = "Self Owned"
    GOVERNMENT_AGENCY = "Government Agency"


# Base filter models
# These models define the structure for each filter type

class BaseFilter(BaseModel):
    """
    Base class for all filters.
    
    Every filter must have a filter_type that identifies what kind of filter it is.
    This helps the API understand how to process the filter.
    """
    filter_type: FilterTypeEnum = Field(
        ...,
        description="The type of filter being applied. This determines how the filter will be processed."
    )


class ListFilter(BaseFilter):
    """
    Base class for filters that accept a list of values with 'in' or 'not in' operations.
    
    These filters allow you to specify multiple values and whether to include or exclude them.
    Example: Find people who work at Google OR Microsoft (type='in')
    Example: Find people who DON'T work at Google OR Microsoft (type='not in')
    """
    type: Literal["in", "not in"] = Field(
        ...,
        description="'in' means the person must match one of the values, 'not in' means they must not match any of the values"
    )
    value: List[str] = Field(
        ...,
        description="List of values to filter by. The person must match (or not match) at least one of these values."
    )


class SingleValueListFilter(BaseFilter):
    """
    Base class for filters that only support 'in' operation with a list of values.
    
    These filters are simpler - they only allow you to search for matches, not exclusions.
    """
    type: Literal["in"] = Field(
        default="in",
        description="These filters only support 'in' operation"
    )
    value: List[str] = Field(
        ...,
        description="List of values to filter by. The person must match at least one of these values."
    )


class BooleanFilter(BaseFilter):
    """
    Base class for boolean filters that don't require additional parameters.
    
    These are simple yes/no filters that check for a specific condition.
    Example: Has the person posted on LinkedIn recently? (no additional parameters needed)
    """
    pass  # No additional fields needed - the filter_type itself indicates what to check


# Specific filter implementations
# Each class below represents a specific filter type with its own validation rules

class CurrentCompanyFilter(ListFilter):
    """
    Filter by the person's current company.
    
    Use this to find people who currently work at specific companies.
    Supports both inclusion (find people at these companies) and exclusion (find people NOT at these companies).
    
    Example:
    - type="in", value=["Google", "Microsoft"] - finds people at Google OR Microsoft
    - type="not in", value=["Meta"] - finds people NOT at Meta
    """
    filter_type: Literal[FilterTypeEnum.CURRENT_COMPANY] = FilterTypeEnum.CURRENT_COMPANY


class CurrentTitleFilter(ListFilter):
    """
    Filter by the person's current job title.
    
    You can use this filter in two ways:
    1. Exact match: Use standardized titles from the Filters Autocomplete API
    2. Fuzzy search: Pass any raw job title text for approximate matching
    
    Example:
    - Exact: value=["Software Engineer", "Senior Software Engineer"]
    - Fuzzy: value=["software dev", "programmer"] - will match variations
    
    Note: For best results with exact matching, use the Filters Autocomplete API to get standardized titles.
    """
    filter_type: Literal[FilterTypeEnum.CURRENT_TITLE] = FilterTypeEnum.CURRENT_TITLE


class PastTitleFilter(ListFilter):
    """
    Filter by titles the person has held in the past.
    
    Similar to CurrentTitleFilter, supports both exact and fuzzy matching.
    Useful for finding people with specific experience even if they've moved to different roles.
    
    Example: Find all people who were previously "Data Scientists" but may have moved to management roles.
    """
    filter_type: Literal[FilterTypeEnum.PAST_TITLE] = FilterTypeEnum.PAST_TITLE


class CompanyHeadquartersFilter(ListFilter):
    """
    Filter by the location of the person's company headquarters.
    
    Important: Use the Filters Autocomplete API to get valid location values.
    The values should be in a specific format recognized by the API.
    
    Example: value=["United States", "Canada", "United Kingdom"]
    
    Note: This filters by where the company is headquartered, not where the person is located.
    """
    filter_type: Literal[FilterTypeEnum.COMPANY_HEADQUARTERS] = FilterTypeEnum.COMPANY_HEADQUARTERS


class CompanyHeadcountFilter(BaseFilter):
    """
    Filter by company size (number of employees).
    
    Only supports 'in' operation - you specify which company sizes to include.
    Uses predefined ranges that represent typical company size categories.
    
    Example: Find people at mid-size companies
    value=["51-200", "201-500"]
    """
    filter_type: Literal[FilterTypeEnum.COMPANY_HEADCOUNT] = FilterTypeEnum.COMPANY_HEADCOUNT
    type: Literal["in"] = Field(default="in", description="Only 'in' operation is supported")
    value: List[CompanyHeadcountEnum] = Field(
        ...,
        description="List of company size ranges. Must use the exact predefined values."
    )
    
    @field_validator('value')
    @classmethod
    def validate_headcount_values(cls, v: List[CompanyHeadcountEnum]) -> List[CompanyHeadcountEnum]:
        """Ensure all headcount values are valid enum members."""
        # The enum type annotation already handles validation
        return v


class RegionFilter(ListFilter):
    """
    Filter by the geographical region where the person is located.
    
    Important: Use the Filters Autocomplete API to get valid region values.
    Regions can be countries, states, cities, or other geographical areas.
    
    Example:
    - value=["California, United States", "Texas, United States"]
    - value=["London, United Kingdom", "Berlin, Germany"]
    
    Note: This is the person's location, not their company's headquarters.
    """
    filter_type: Literal[FilterTypeEnum.REGION] = FilterTypeEnum.REGION


class IndustryFilter(ListFilter):
    """
    Filter by the industry of the person's company.
    
    Important: Use the Filters Autocomplete API to get valid industry values.
    Industries should match LinkedIn's standardized industry categories.
    
    Example: value=["Software Development", "Financial Services", "Healthcare"]
    """
    filter_type: Literal[FilterTypeEnum.INDUSTRY] = FilterTypeEnum.INDUSTRY


class ProfileLanguageFilter(BaseFilter):
    """
    Filter by the language of the person's LinkedIn profile.
    
    Only supports 'in' operation - select which languages to include.
    Limited to the languages that LinkedIn supports for profiles.
    
    Example: Find people with English or Spanish profiles
    value=["English", "Spanish"]
    """
    filter_type: Literal[FilterTypeEnum.PROFILE_LANGUAGE] = FilterTypeEnum.PROFILE_LANGUAGE
    type: Literal["in"] = Field(default="in", description="Only 'in' operation is supported")
    value: List[ProfileLanguageEnum] = Field(
        ...,
        description="List of languages. Must use the exact predefined language names."
    )


class SeniorityLevelFilter(ListFilter):
    """
    Filter by the person's seniority level in their organization.
    
    Seniority levels represent the hierarchical position, from entry-level to C-suite.
    Supports both inclusion and exclusion.
    
    Example:
    - Find executives: type="in", value=["CXO", "Vice President"]
    - Exclude entry-level: type="not in", value=["Entry Level", "In Training"]
    """
    filter_type: Literal[FilterTypeEnum.SENIORITY_LEVEL] = FilterTypeEnum.SENIORITY_LEVEL
    value: List[SeniorityLevelEnum] = Field(
        ...,
        description="List of seniority levels. Must use the exact predefined values."
    )


class YearsAtCurrentCompanyFilter(BaseFilter):
    """
    Filter by how long the person has been at their current company.
    
    Useful for finding people who might be ready for a change (long tenure)
    or those who recently joined (short tenure).
    
    Example: Find people who've been at their company 3-5 years
    value=["3 to 5 years"]
    """
    filter_type: Literal[FilterTypeEnum.YEARS_AT_CURRENT_COMPANY] = FilterTypeEnum.YEARS_AT_CURRENT_COMPANY
    type: Literal["in"] = Field(default="in", description="Only 'in' operation is supported")
    value: List[YearsRangeEnum] = Field(
        ...,
        description="List of year ranges. Must use the exact predefined ranges."
    )


class YearsInCurrentPositionFilter(BaseFilter):
    """
    Filter by how long the person has been in their current role/position.
    
    Different from company tenure - someone might be at a company for 10 years
    but only in their current position for 2 years.
    
    Example: Find people who recently got promoted (less than 1 year in current position)
    value=["Less than 1 year"]
    """
    filter_type: Literal[FilterTypeEnum.YEARS_IN_CURRENT_POSITION] = FilterTypeEnum.YEARS_IN_CURRENT_POSITION
    type: Literal["in"] = Field(default="in", description="Only 'in' operation is supported")
    value: List[YearsRangeEnum] = Field(
        ...,
        description="List of year ranges. Must use the exact predefined ranges."
    )


class YearsOfExperienceFilter(BaseFilter):
    """
    Filter by the person's total years of professional experience.
    
    This is their entire career length, not just at current company or position.
    
    Example: Find mid-career professionals
    value=["3 to 5 years", "6 to 10 years"]
    """
    filter_type: Literal[FilterTypeEnum.YEARS_OF_EXPERIENCE] = FilterTypeEnum.YEARS_OF_EXPERIENCE
    type: Literal["in"] = Field(default="in", description="Only 'in' operation is supported")
    value: List[YearsRangeEnum] = Field(
        ...,
        description="List of year ranges. Must use the exact predefined ranges."
    )


class FirstNameFilter(BaseFilter):
    """
    Filter by the person's first name.
    
    Only supports exact matches and a single name at a time.
    Case-sensitive depending on the API implementation.
    
    Example: value=["John"]
    """
    filter_type: Literal[FilterTypeEnum.FIRST_NAME] = FilterTypeEnum.FIRST_NAME
    type: Literal["in"] = Field(default="in", description="Only 'in' operation is supported")
    value: List[str] = Field(
        ...,
        description="First name to search for. List must contain exactly one name.",
        max_length=1,
        min_length=1
    )


class LastNameFilter(BaseFilter):
    """
    Filter by the person's last name.
    
    Only supports exact matches and a single name at a time.
    Case-sensitive depending on the API implementation.
    
    Example: value=["Smith"]
    """
    filter_type: Literal[FilterTypeEnum.LAST_NAME] = FilterTypeEnum.LAST_NAME
    type: Literal["in"] = Field(default="in", description="Only 'in' operation is supported")
    value: List[str] = Field(
        ...,
        description="Last name to search for. List must contain exactly one name.",
        max_length=1,
        min_length=1
    )


class FunctionFilter(ListFilter):
    """
    Filter by the person's job function or department.
    
    Functions are broader categories than specific titles.
    For example, "Engineering" function includes software engineers, QA engineers, DevOps, etc.
    
    Example: Find anyone in technical roles
    type="in", value=["Engineering", "Information Technology"]
    """
    filter_type: Literal[FilterTypeEnum.FUNCTION] = FilterTypeEnum.FUNCTION
    value: List[FunctionEnum] = Field(
        ...,
        description="List of job functions. Must use the exact predefined function names."
    )


class PastCompanyFilter(ListFilter):
    """
    Filter by companies the person has worked for in the past.
    
    Useful for finding people with experience at specific companies,
    even if they no longer work there.
    
    Example: Find people who used to work at startups that got acquired
    type="in", value=["WhatsApp", "Instagram", "LinkedIn"]
    """
    filter_type: Literal[FilterTypeEnum.PAST_COMPANY] = FilterTypeEnum.PAST_COMPANY


class CompanyTypeFilter(BaseFilter):
    """
    Filter by the type of organization the person works for.
    
    Helps distinguish between different organizational structures.
    
    Example: Find people at non-profits and educational institutions
    value=["Non Profit", "Educational Institution"]
    """
    filter_type: Literal[FilterTypeEnum.COMPANY_TYPE] = FilterTypeEnum.COMPANY_TYPE
    type: Literal["in"] = Field(default="in", description="Only 'in' operation is supported")
    value: List[CompanyTypeEnum] = Field(
        ...,
        description="List of company types. Must use the exact predefined values."
    )


class PostedOnLinkedInFilter(BooleanFilter):
    """
    Filter for people who have posted on LinkedIn in the last 30 days.
    
    This is a boolean filter - if included, it filters for active LinkedIn users.
    No additional parameters needed.
    
    Useful for finding engaged professionals who are active on the platform.
    """
    filter_type: Literal[FilterTypeEnum.POSTED_ON_LINKEDIN] = FilterTypeEnum.POSTED_ON_LINKEDIN


class RecentlyChangedJobsFilter(BooleanFilter):
    """
    Filter for people who have changed jobs in the last 90 days.
    
    This is a boolean filter - if included, it finds people with recent job changes.
    No additional parameters needed.
    
    Useful for outreach to people in new roles who might need services/products.
    """
    filter_type: Literal[FilterTypeEnum.RECENTLY_CHANGED_JOBS] = FilterTypeEnum.RECENTLY_CHANGED_JOBS


class InTheNewsFilter(BooleanFilter):
    """
    Filter for people who have been mentioned in the news.
    
    This is a boolean filter - if included, it finds people with news mentions.
    No additional parameters needed.
    
    Useful for finding high-profile individuals or those involved in newsworthy events.
    """
    filter_type: Literal[FilterTypeEnum.IN_THE_NEWS] = FilterTypeEnum.IN_THE_NEWS


class KeywordFilter(BaseFilter):
    """
    Filter based on specific keywords related to the person or their company.
    
    This is a general text search across various fields in the person's profile.
    Only supports a single keyword/phrase at a time.
    
    Example: value=["machine learning"] - finds people with "machine learning" mentioned in their profile
    """
    filter_type: Literal[FilterTypeEnum.KEYWORD] = FilterTypeEnum.KEYWORD
    type: Literal["in"] = Field(default="in", description="Only 'in' operation is supported")
    value: List[str] = Field(
        ...,
        description="Keyword or phrase to search for. List must contain exactly one keyword.",
        max_length=1,
        min_length=1
    )


class SchoolFilter(BaseFilter):
    """
    Filter by the school/university the person attended.
    
    Note: If the exact school name is not found, the API will return suggestions.
    Use one of the suggested values and retry the search.
    
    Example: value=["Stanford University"]
    If not found exactly, API might suggest: ["Stanford University", "Stanford Graduate School of Business"]
    """
    filter_type: Literal[FilterTypeEnum.SCHOOL] = FilterTypeEnum.SCHOOL
    type: Literal["in"] = Field(default="in", description="Only 'in' operation is supported")
    value: List[str] = Field(
        ...,
        description="School name to search for. List must contain exactly one school. Use suggestions from API if exact match not found.",
        max_length=1,
        min_length=1
    )


# Union type for all possible filters
# This allows the API to accept any valid filter type

FilterType = Union[
    CurrentCompanyFilter,
    CurrentTitleFilter,
    PastTitleFilter,
    CompanyHeadquartersFilter,
    CompanyHeadcountFilter,
    RegionFilter,
    IndustryFilter,
    ProfileLanguageFilter,
    SeniorityLevelFilter,
    YearsAtCurrentCompanyFilter,
    YearsInCurrentPositionFilter,
    YearsOfExperienceFilter,
    FirstNameFilter,
    LastNameFilter,
    FunctionFilter,
    PastCompanyFilter,
    CompanyTypeFilter,
    PostedOnLinkedInFilter,
    RecentlyChangedJobsFilter,
    InTheNewsFilter,
    KeywordFilter,
    SchoolFilter
]


# Request model for the search API
# This is the main model that combines all filters into a search request

class PersonSearchRequest(BaseModel):
    """
    Main request model for person search API.
    
    This model represents a complete search request with multiple filters and pagination.
    All filters are combined with AND logic - a person must match ALL filters to be included.
    
    Example usage:
    {
        "filters": [
            {
                "filter_type": "CURRENT_COMPANY",
                "type": "in",
                "value": ["Google", "Microsoft"]
            },
            {
                "filter_type": "SENIORITY_LEVEL",
                "type": "in", 
                "value": ["Director", "Vice President"]
            },
            {
                "filter_type": "REGION",
                "type": "in",
                "value": ["California, United States"]
            }
        ],
        "page": 1
    }
    
    This would find Directors or VPs at Google or Microsoft located in California.
    """
    filters: List[FilterType] = Field(
        ...,
        description="List of filters to apply. All filters are combined with AND logic.",
        min_length=1
    )
    page: int = Field(
        default=1,
        ge=1,
        description="Page number for pagination. Starts at 1."
    )
    
    @field_validator('filters')
    @classmethod
    def validate_unique_filter_types(cls, v: List[FilterType]) -> List[FilterType]:
        """
        Ensure no duplicate filter types in the same request.
        
        Each filter type should appear at most once in a search request.
        For example, you can't have two CURRENT_COMPANY filters.
        """
        filter_types = [f.filter_type for f in v]
        if len(filter_types) != len(set(filter_types)):
            raise ValueError("Duplicate filter types are not allowed in the same request")
        return v


# Helper function to create filters programmatically
# This can be useful for the LLM or for testing

def create_filter(filter_type: str, **kwargs) -> FilterType:
    """
    Factory function to create a filter instance based on the filter type.
    
    Args:
        filter_type: The type of filter to create (e.g., "CURRENT_COMPANY")
        **kwargs: Additional arguments specific to the filter type
        
    Returns:
        An instance of the appropriate filter class
        
    Example:
        filter = create_filter(
            "CURRENT_COMPANY",
            type="in",
            value=["Google", "Apple"]
        )
    """
    filter_map = {
        "CURRENT_COMPANY": CurrentCompanyFilter,
        "CURRENT_TITLE": CurrentTitleFilter,
        "PAST_TITLE": PastTitleFilter,
        "COMPANY_HEADQUARTERS": CompanyHeadquartersFilter,
        "COMPANY_HEADCOUNT": CompanyHeadcountFilter,
        "REGION": RegionFilter,
        "INDUSTRY": IndustryFilter,
        "PROFILE_LANGUAGE": ProfileLanguageFilter,
        "SENIORITY_LEVEL": SeniorityLevelFilter,
        "YEARS_AT_CURRENT_COMPANY": YearsAtCurrentCompanyFilter,
        "YEARS_IN_CURRENT_POSITION": YearsInCurrentPositionFilter,
        "YEARS_OF_EXPERIENCE": YearsOfExperienceFilter,
        "FIRST_NAME": FirstNameFilter,
        "LAST_NAME": LastNameFilter,
        "FUNCTION": FunctionFilter,
        "PAST_COMPANY": PastCompanyFilter,
        "COMPANY_TYPE": CompanyTypeFilter,
        "POSTED_ON_LINKEDIN": PostedOnLinkedInFilter,
        "RECENTLY_CHANGED_JOBS": RecentlyChangedJobsFilter,
        "IN_THE_NEWS": InTheNewsFilter,
        "KEYWORD": KeywordFilter,
        "SCHOOL": SchoolFilter,
    }
    
    filter_class = filter_map.get(filter_type)
    if not filter_class:
        raise ValueError(f"Unknown filter type: {filter_type}")
    
    return filter_class(**kwargs)


# Example usage for LLM
# These examples show how an LLM should construct filters

"""
Example 1: Find Software Engineers at FAANG companies in the Bay Area
-------------------------------------------------------------------
request = PersonSearchRequest(
    filters=[
        CurrentTitleFilter(type="in", value=["Software Engineer", "Senior Software Engineer"]),
        CurrentCompanyFilter(type="in", value=["Google", "Apple", "Amazon", "Netflix", "Meta"]),
        RegionFilter(type="in", value=["San Francisco Bay Area"])
    ],
    page=1
)

Example 2: Find executives who recently changed jobs and are active on LinkedIn
-----------------------------------------------------------------------------
request = PersonSearchRequest(
    filters=[
        SeniorityLevelFilter(type="in", value=["CXO", "Vice President", "Director"]),
        RecentlyChangedJobsFilter(),
        PostedOnLinkedInFilter()
    ],
    page=1
)

Example 3: Find mid-level professionals NOT at big tech companies
---------------------------------------------------------------
request = PersonSearchRequest(
    filters=[
        YearsOfExperienceFilter(type="in", value=["3 to 5 years", "6 to 10 years"]),
        CurrentCompanyFilter(type="not in", value=["Google", "Microsoft", "Amazon", "Apple", "Meta"]),
        FunctionFilter(type="in", value=["Engineering", "Product Management"])
    ],
    page=1
)
"""
