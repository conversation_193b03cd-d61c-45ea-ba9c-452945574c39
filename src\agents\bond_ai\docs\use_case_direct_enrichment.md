# Use Case: Direct LinkedIn Profile Enrichment

## Overview

This document demonstrates the Supervisor Agentic Pattern implementation for a direct LinkedIn profile enrichment scenario. The use case showcases how the Supervisor Agent orchestrates multiple specialized agents to analyze existing table data and enrich LinkedIn profiles efficiently.

## Scenario Specification

### **Scenario Name**: Direct LinkedIn Profile Enrichment (Simple Path)

### **Existing Table Data Structure**:
- **Column 1**: `LinkedIn Profile URLs` - Contains valid LinkedIn profile URLs
- **Column 2**: `Full Name` - Person names extracted or manually entered
- **Column 3**: `Job Title` - Current job titles of prospects

### **User Request**: 
*"Fetch/enrich the LinkedIn profile of people in the table"*

### **Expected Workflow**:
Supervisor Agent → Data Management Agent (table analysis) → Enrichment Agent (LinkedIn profile import) → Execution Agent (column execution)

## Agent Workflow Implementation

### 1. Supervisor Agent Analysis

The Supervisor Agent receives the user request and performs initial analysis:

```python
# Supervisor Agent Decision Process
user_request = "Fetch/enrich the LinkedIn profile of people in the table"

# Analysis criteria:
# - Request involves LinkedIn profile data → Enrichment Agent required
# - Need to understand table structure → Data Management Agent required  
# - Will need to execute columns → Execution Agent required
# - No web research needed → Research Agent not required
# - No content generation needed → Content Agent not required

delegation_plan = {
    "task_1": {
        "agent": "data_management_agent",
        "action": "analyze_table_structure",
        "tools": ["read_table_data", "read_user_view_table_filters"]
    },
    "task_2": {
        "agent": "enrichment_agent", 
        "action": "create_linkedin_profile_column",
        "tools": ["upsert_linkedin_person_profile_column_from_url"],
        "dependencies": ["task_1"]
    },
    "task_3": {
        "agent": "execution_agent",
        "action": "execute_linkedin_enrichment",
        "tools": ["run_column"],
        "dependencies": ["task_2"]
    }
}
```

### 2. Data Management Agent Task

**Delegation Message from Supervisor**:
```
DELEGATE_TO: data_management_agent
TASK: Analyze current table structure and identify LinkedIn profile enrichment opportunities
CONTEXT: User wants to enrich LinkedIn profiles for existing prospects
EXPECTED_OUTPUT: Table schema analysis with column mapping and row count
PRIORITY: high
```

**Data Management Agent Actions**:
1. **Tool**: `read_user_view_table_filters()` - Check current table filters
2. **Tool**: `read_table_data(max_rows=10, summarize=True)` - Analyze table structure
3. **Analysis**: Identify existing columns and data quality
4. **Output**: Structured analysis for enrichment planning

### 3. Enrichment Agent Task

**Delegation Message from Supervisor**:
```
DELEGATE_TO: enrichment_agent
TASK: Create LinkedIn profile enrichment column using existing LinkedIn URLs
CONTEXT: Table contains LinkedIn Profile URLs column with valid URLs
EXPECTED_OUTPUT: New LinkedIn profile column with comprehensive data extraction
PRIORITY: high
```

**Enrichment Agent Actions**:
1. **Tool**: `upsert_linkedin_person_profile_column_from_url()`
   - `column_name`: "LinkedIn Profile Data"
   - `linkedin_profile_url`: "{{LinkedIn Profile URLs.cell_value}}"
   - `column_id`: None (new column creation)
2. **Validation**: Verify injection path accuracy
3. **Output**: Column creation confirmation with column ID

### 4. Execution Agent Task

**Delegation Message from Supervisor**:
```
DELEGATE_TO: execution_agent
TASK: Execute LinkedIn profile enrichment for all table rows
CONTEXT: LinkedIn profile column created, ready for data population
EXPECTED_OUTPUT: Execution completion with status report
PRIORITY: high
```

**Execution Agent Actions**:
1. **Pre-execution Validation**: Verify column runnability
2. **User Confirmation**: Request approval for batch execution
3. **Tool**: `run_column(column_id="new_column_id", count=row_count, wait_results=True)`
4. **Monitoring**: Track execution progress and handle errors
5. **Output**: Execution summary with success/failure details

## Detailed Sequence Diagram

```mermaid
sequenceDiagram
    participant User
    participant Supervisor
    participant Planner
    participant DataMgmt as Data Management Agent
    participant Enrichment as Enrichment Agent
    participant Execution as Execution Agent
    participant Database
    participant LinkedIn as LinkedIn API

    User->>Supervisor: "Fetch/enrich the LinkedIn profile of people in the table"
    
    Note over Supervisor: Analyze request and determine required agents
    Supervisor->>Planner: Create execution plan for LinkedIn enrichment
    Planner-->>Supervisor: Plan: DataMgmt → Enrichment → Execution
    
    Note over Supervisor: Task 1: Table Analysis
    Supervisor->>DataMgmt: DELEGATE: Analyze table structure
    DataMgmt->>Database: read_user_view_table_filters()
    Database-->>DataMgmt: Current filters: none
    DataMgmt->>Database: read_table_data(max_rows=10, summarize=True)
    Database-->>DataMgmt: Schema: LinkedIn URLs, Names, Job Titles (50 rows)
    DataMgmt-->>Supervisor: Analysis: 50 prospects with LinkedIn URLs ready for enrichment
    
    Note over Supervisor: Task 2: Column Creation
    Supervisor->>Enrichment: DELEGATE: Create LinkedIn profile column
    Enrichment->>Database: upsert_linkedin_person_profile_column_from_url()
    Note over Enrichment: column_name: "LinkedIn Profile Data"<br/>linkedin_profile_url: "{{LinkedIn Profile URLs.cell_value}}"
    Database-->>Enrichment: Column created (ID: 123)
    Enrichment-->>Supervisor: LinkedIn profile column ready (ID: 123)
    
    Note over Supervisor: Task 3: Execution
    Supervisor->>Execution: DELEGATE: Execute LinkedIn enrichment
    Execution->>Database: Validate column runnability (ID: 123)
    Database-->>Execution: Column is runnable
    
    Execution->>User: CONFIRM: Execute LinkedIn enrichment for 50 rows?
    User-->>Execution: Confirmed
    
    Execution->>Database: run_column(column_id=123, count=50, wait_results=True)
    Database->>LinkedIn: Batch LinkedIn profile requests
    LinkedIn-->>Database: Profile data responses
    Database-->>Execution: Execution complete: 48 success, 2 failed
    
    Execution-->>Supervisor: Execution summary: 96% success rate
    Supervisor->>User: "LinkedIn enrichment complete: 48/50 profiles enriched successfully"
```

## State Management Throughout Workflow

### Initial State
```python
initial_state = {
    "messages": [HumanMessage("Fetch/enrich the LinkedIn profile of people in the table")],
    "table_summary": None,
    "mode": None,
    "selected_row_ids": None,
    "selected_column_ids": None,
    "execution_plan": None,
    "current_task_id": None,
    "task_status": None,
    "active_agent": None
}
```

### State After Planning
```python
post_planning_state = {
    "execution_plan": {
        "id": "linkedin_enrichment_001",
        "tasks": [
            {"id": "task_1", "agent": "data_management_agent", "status": "pending"},
            {"id": "task_2", "agent": "enrichment_agent", "status": "pending"},
            {"id": "task_3", "agent": "execution_agent", "status": "pending"}
        ]
    },
    "current_task_id": "task_1",
    "active_agent": "data_management_agent"
}
```

### State After Completion
```python
final_state = {
    "task_status": [
        {"id": "task_1", "status": "completed", "agent": "data_management_agent"},
        {"id": "task_2", "status": "completed", "agent": "enrichment_agent"},
        {"id": "task_3", "status": "completed", "agent": "execution_agent"}
    ],
    "agent_outputs": {
        "data_management_agent": "50 prospects identified with LinkedIn URLs",
        "enrichment_agent": "LinkedIn profile column created (ID: 123)",
        "execution_agent": "48/50 profiles enriched successfully"
    },
    "active_agent": None
}
```

## Tool Mapping and Agent Interactions

### Data Management Agent Implementation

**Agent Selection Rationale**:
- Owns table analysis tools (`read_table_data`, `read_user_view_table_filters`)
- Specializes in data structure understanding and optimization
- Required for understanding existing table schema before enrichment

**Tool Execution Sequence**:
```python
# Step 1: Check current table filters
filters_result = read_user_view_table_filters(config)
# Expected output: Current filter state (likely empty for this scenario)

# Step 2: Analyze table structure
table_analysis = read_table_data(
    config=config,
    max_rows=10,  # Sample for schema analysis
    summarize=True,  # Get AI-powered summary
    filters=None,  # Use current user filters
    column_ids=None  # Analyze all columns
)
# Expected output: Table schema with column descriptions and data quality assessment
```

**Agent Output Format**:
```json
{
    "table_analysis": {
        "total_rows": 50,
        "columns": [
            {
                "name": "LinkedIn Profile URLs",
                "type": "text",
                "data_quality": "95% valid URLs",
                "sample_data": "https://linkedin.com/in/john-doe"
            },
            {
                "name": "Full Name",
                "type": "text",
                "data_quality": "100% populated",
                "sample_data": "John Doe"
            },
            {
                "name": "Job Title",
                "type": "text",
                "data_quality": "90% populated",
                "sample_data": "Marketing Manager"
            }
        ],
        "enrichment_readiness": "Ready for LinkedIn profile enrichment",
        "recommended_injection_path": "{{LinkedIn Profile URLs.cell_value}}"
    }
}
```

### Enrichment Agent Implementation

**Agent Selection Rationale**:
- Owns LinkedIn enrichment tools (`upsert_linkedin_person_profile_column_from_url`)
- Specializes in contact data discovery and profile imports
- Expert in injection path validation and data enrichment strategies

**Tool Execution Details**:
```python
# LinkedIn Profile Column Creation
enrichment_result = upsert_linkedin_person_profile_column_from_url(
    config=config,
    column_name="LinkedIn Profile Data",
    linkedin_profile_url="{{LinkedIn Profile URLs.cell_value}}",
    column_id=None  # Creating new column
)

# Validation Process:
# 1. Validate injection sequence: "{{LinkedIn Profile URLs.cell_value}}"
# 2. Check target data type: LINKEDIN_PROFILE_URL
# 3. Set up service configuration (service_id=7, provider="outbond")
# 4. Create smart column with proper inputs and parameters
```

**Agent Output Format**:
```json
{
    "column_creation": {
        "action": "created",
        "column_name": "LinkedIn Profile Data",
        "column_id": 123,
        "column_type": "linkedin_person_profile",
        "injection_path": "{{LinkedIn Profile URLs.cell_value}}",
        "provider": "outbond",
        "validation_status": "passed",
        "message": "LinkedIn profile column successfully created and ready for execution"
    }
}
```

### Execution Agent Implementation

**Agent Selection Rationale**:
- Owns column execution tool (`run_column`)
- Specializes in batch operations, monitoring, and user confirmations
- Expert in execution status tracking and error handling

**Tool Execution Process**:
```python
# Pre-execution Validation
column_validation = validate_column_runnability(column_id=123)

# User Confirmation Protocol
confirmation_message = {
    "column_name": "LinkedIn Profile Data",
    "column_id": 123,
    "rows_count": 50,
    "message": "Do you want to run the column 'LinkedIn Profile Data' for 50 rows?"
}
user_response = interrupt(confirmation_message)

# Column Execution (if confirmed)
if user_confirmed:
    execution_result = run_column(
        config=config,
        column_id="123",
        column_name="LinkedIn Profile Data",
        count=50,
        wait_results=True  # Wait for completion and provide detailed results
    )
```

**Agent Output Format**:
```json
{
    "execution_summary": {
        "column_name": "LinkedIn Profile Data",
        "total_rows": 50,
        "successful_rows": 48,
        "failed_rows": 2,
        "success_rate": "96%",
        "execution_time": "2.5 minutes",
        "status_breakdown": {
            "completed": 48,
            "failed": 2,
            "awaiting_input": 0
        },
        "error_details": [
            {"row_id": 15, "error": "Invalid LinkedIn URL format"},
            {"row_id": 32, "error": "Profile not accessible"}
        ]
    }
}
```

## Communication Protocols

### Supervisor-to-Agent Delegation Format

Each task delegation follows the standardized format defined in the supervisor pattern:

```python
delegation_message = f"""
DELEGATE_TO: {agent_name}
TASK: {task_description}
CONTEXT: {relevant_context}
EXPECTED_OUTPUT: {expected_output_format}
PRIORITY: {priority_level}
TOOLS: {required_tools}
DEPENDENCIES: {task_dependencies}
"""
```

### Agent-to-Supervisor Response Format

```python
agent_response = {
    "task_id": "task_1",
    "agent": "data_management_agent",
    "status": "completed",
    "output": {
        "summary": "Table analysis complete",
        "details": {...},
        "next_steps": "Ready for enrichment column creation"
    },
    "execution_time": 15.2,
    "errors": None
}
```

## Error Handling and Fallback Strategies

### Data Management Agent Errors

**Scenario**: Table access issues or empty table
```python
error_handling = {
    "table_not_found": "Fallback to basic schema analysis",
    "permission_denied": "Request user to check table access",
    "empty_table": "Inform user no data available for enrichment"
}
```

### Enrichment Agent Errors

**Scenario**: Invalid LinkedIn URLs or column creation failures
```python
error_handling = {
    "invalid_injection_path": "Suggest alternative column mapping",
    "linkedin_url_validation_failed": "Provide data cleaning recommendations",
    "column_creation_failed": "Retry with simplified configuration"
}
```

### Execution Agent Errors

**Scenario**: LinkedIn API limits or execution failures
```python
error_handling = {
    "api_rate_limit": "Implement exponential backoff and retry",
    "execution_timeout": "Provide partial results and retry failed rows",
    "user_cancellation": "Save progress and provide cancellation summary"
}
```

## Validation Against Supervisor Pattern

### ✅ Agent Assignment Validation

- **Data Management Agent**: Correctly assigned for table analysis (owns `read_table_data`, `read_user_view_table_filters`)
- **Enrichment Agent**: Correctly assigned for LinkedIn enrichment (owns `upsert_linkedin_person_profile_column_from_url`)
- **Execution Agent**: Correctly assigned for column execution (owns `run_column`)
- **Research Agent**: Correctly excluded (no web search required)
- **Content Agent**: Correctly excluded (no content generation required)

### ✅ Tool Mapping Validation

All tools used match the agent tool mappings defined in `supervisor_agentic_pattern.md`:

```python
# From supervisor pattern AGENT_TOOL_MAPPING
"data_management_agent": ["read_table_data", "read_user_view_table_filters", "update_user_view_table_filters_tool"]
"enrichment_agent": ["upsert_linkedin_person_profile_column_from_url", ...]
"execution_agent": ["run_column"]
```

### ✅ State Management Validation

The workflow follows the `SupervisorAgentState` structure:
- ✅ `execution_plan` properly populated by planner
- ✅ `current_task_id` tracks active task
- ✅ `task_status` updates as tasks complete
- ✅ `active_agent` indicates current delegation
- ✅ `agent_outputs` captures results from each agent

### ✅ Communication Protocol Validation

All inter-agent communications follow the standardized delegation syntax and response formats defined in the supervisor pattern.

## Expected User Experience

### Timeline and Progress Updates

```
[00:00] User: "Fetch/enrich the LinkedIn profile of people in the table"
[00:01] Supervisor: "Creating execution plan..."
[00:02] Supervisor: "Analyzing table structure..."
[00:05] Data Management Agent: "Found 50 prospects with LinkedIn URLs"
[00:06] Supervisor: "Creating LinkedIn profile enrichment column..."
[00:08] Enrichment Agent: "LinkedIn profile column created successfully"
[00:09] Supervisor: "Ready to execute enrichment. Confirm execution for 50 rows?"
[00:10] User: "Confirmed"
[00:11] Execution Agent: "Starting LinkedIn profile enrichment..."
[02:35] Execution Agent: "Enrichment complete: 48/50 profiles enriched successfully"
[02:36] Supervisor: "LinkedIn enrichment complete: 48/50 profiles enriched successfully"
```

### Final Result

The user receives a fully enriched table with:
- Original columns preserved (LinkedIn URLs, Names, Job Titles)
- New "LinkedIn Profile Data" column with comprehensive profile information
- Detailed execution summary with success/failure breakdown
- Actionable insights for failed enrichments

This use case demonstrates the power of the Supervisor Agentic Pattern in orchestrating complex data enrichment workflows while maintaining user control and providing comprehensive feedback throughout the process.
```
