# Outbond AI Assistant - Technical Documentation

## Overview

The Outbond AI Assistant is a LangGraph-based ReAct (Reasoning and Acting) agent designed to help users manage outbound sales campaigns within Outbond's no-code workspace. The agent operates on table data and provides intelligent automation for prospect research, data enrichment, and outreach campaign management.

## Architecture

### Core Components

- **Graph Implementation**: `src/outbond_ai_assistant/graph.py`
- **State Management**: `src/outbond_ai_assistant/state.py`
- **Tools**: `src/outbond_ai_assistant/tools.py`
- **Configuration**: `src/outbond_ai_assistant/configuration.py`
- **Prompts**: `src/outbond_ai_assistant/prompts.py`

## Agent Logic Analysis

### State Management (`AgentState`)

The agent uses a TypedDict-based state structure with the following key components:

```python
class AgentState(TypedDict):
    messages: Annotated[Sequence[BaseMessage], add_messages]
    table_summary: Annotated[Optional[str], preserve_table_summary]
    mode: Optional[str]
    selected_row_ids: Optional[int]
    selected_column_ids: Optional[str]
```

**State Fields:**
- `messages`: Conversation history with automatic message aggregation
- `table_summary`: Cached table schema and data summary for efficient operations
- `mode`: Operating mode ("chat" for conversational mode, None for tool mode)
- `selected_row_ids`: User-selected table rows for focused operations
- `selected_column_ids`: User-selected table columns for targeted actions

### Workflow Nodes

#### 1. Table Indexing Node (`table_indexing_node`)

**Purpose**: Analyzes table structure and creates intelligent summaries for agent context.

**Responsibilities:**
- Retrieves table column metadata
- Generates AI-powered column descriptions for columns lacking summaries
- Creates comprehensive table schema with data insights
- Updates column descriptions in the database
- Provides fresh table summary for agent operations

**Key Features:**
- Only processes columns without existing descriptions (efficiency optimization)
- Uses structured analysis with 10-row samples
- Combines fresh schema data with stored summaries
- Validates all columns have descriptions before proceeding

#### 2. Model Call Node (`call_model`)

**Purpose**: Orchestrates LLM interactions with context-aware prompt engineering.

**Responsibilities:**
- Loads appropriate chat model based on configuration
- Handles chat mode vs tool mode differentiation
- Constructs context-rich system prompts
- Manages selected rows/columns context
- Implements error handling with graceful fallbacks

**Context Management:**
- Injects table summary, current filters, and selected data context
- Cleans thinking blocks for Bedrock compatibility
- Adds mode-specific instructions (chat vs tool mode)

#### 3. Tool Execution Node (`tool_node`)

**Purpose**: Executes tools based on model decisions with user confirmation for critical actions.

**Responsibilities:**
- Processes tool calls from AI messages
- Implements user confirmation for `run_column` operations
- Executes tools with comprehensive error handling
- Returns structured tool results

**Special Features:**
- Interactive confirmation for column execution with row count details
- Graceful error handling with detailed error messages
- Support for multiple tool calls in sequence

### Edge Conditions and Routing Logic

#### Conditional Edge (`should_continue`)

**Decision Logic:**
1. **Chat Mode**: Always routes to END (no tool execution)
2. **Tool Calls Present**: Routes to "tools" node for execution
3. **No Tool Calls**: Routes to END (conversation complete)

**Flow Diagram:**
```
table_indexing → agent → [should_continue] → tools → agent
                      ↓                           ↑
                     END ←─────────────────────────┘
```

### Error Handling and Fallback Mechanisms

1. **Table Indexing Failures**: Comprehensive exception handling with detailed error messages
2. **Model Call Failures**: Automatic fallback with cleaned message history
3. **Tool Execution Failures**: Individual tool error capture with continued execution
4. **User Cancellation**: Graceful handling of cancelled operations

## Tool Documentation

### Core Tools Overview

The agent has access to 16 specialized tools organized into categories:

#### Data Access Tools
1. **`read_table_data`**: Fetches table data with filtering, sorting, and summarization
2. **`read_user_view_table_filters`**: Retrieves current table view filters
3. **`update_user_view_table_filters_tool`**: Updates table view filters

#### Web Research Tools
4. **`search`**: General web search using Tavily search engine
5. **`scrape_website`**: Website content extraction using FireCrawl

#### LinkedIn Integration Tools
6. **`search_linkedin_profiles`**: Bulk LinkedIn profile discovery
7. **`upsert_linkedin_person_profile_column_from_url`**: Import LinkedIn person profiles
8. **`upsert_linkedin_company_profile_column_from_url`**: Import LinkedIn company profiles

#### Contact Discovery Tools
9. **`upsert_phone_number_column`**: Phone number discovery from LinkedIn profiles
10. **`upsert_work_email_column`**: Work email discovery using name and company domain

#### Content Generation Tools
11. **`upsert_text_column`**: Static text or formula-based columns
12. **`upsert_ai_text_column`**: AI-generated content columns
13. **`upsert_bond_ai_researcher_column`**: Prospect research insights
14. **`upsert_ai_message_copywriter`**: Personalized outreach copy

#### Execution Tools
15. **`run_column`**: Execute smart columns with optional result waiting
16. **`upsert_text_column`**: (Duplicate for legacy compatibility)

### Tool Integration Points

**Configuration Injection**: All tools receive `RunnableConfig` with table_id and model settings
**Stream Writing**: Tools provide real-time status updates via `get_stream_writer()`
**Validation**: Input validation using Pydantic models and injection sequence validation
**Error Handling**: Consistent error response format across all tools

### Tool Dependencies

- **Database Layer**: `agent_db.py` functions for data persistence
- **External APIs**: Tavily (search), FireCrawl (scraping), LinkedIn APIs
- **AI Models**: OpenAI GPT-4o-mini for summarization, Bedrock Claude for main operations
- **Validation Services**: Injection sequence validation for data integrity

## User Interaction Flow

### Scenario 1: Building a Prospect List

**User Query**: "Help me find 50 marketing managers at SaaS companies in San Francisco"

**Expected Flow:**
1. **Table Indexing**: Analyze current table structure
2. **Agent Planning**: Understand requirements and plan LinkedIn search
3. **Tool Execution**: `search_linkedin_profiles` with appropriate filters
4. **Profile Import**: `upsert_linkedin_person_profile_column_from_url` for discovered profiles
5. **Data Enrichment**: `upsert_work_email_column` and `upsert_phone_number_column`
6. **Execution**: `run_column` to populate the enrichment data

### Scenario 2: Creating Personalized Outreach

**User Query**: "Create personalized LinkedIn messages for my prospects"

**Expected Flow:**
1. **Table Analysis**: Review existing prospect data
2. **Research Enhancement**: `upsert_bond_ai_researcher_column` for prospect insights
3. **Message Creation**: `upsert_ai_message_copywriter` with personalization prompts
4. **Execution**: `run_column` to generate personalized messages
5. **Review**: Present results for user approval

### Scenario 3: Data Filtering and Analysis

**User Query**: "Show me only prospects from companies with 100+ employees"

**Expected Flow:**
1. **Current Filters**: `read_user_view_table_filters` to understand current state
2. **Filter Update**: `update_user_view_table_filters_tool` with company size criteria
3. **Data Review**: `read_table_data` with applied filters for verification

## Configuration

### Default Settings
- **Table ID**: `tbl_c7e3cfcc8a402da0`
- **Model**: `bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0`
- **Max Search Results**: 5 (configurable)

### Environment Variables
- `FIRECRAWL_API_KEY`: For website scraping functionality
- `CRUSTDATA_API_KEY`: For company data enrichment

## Detailed User Scenarios with Sequence Diagrams

### Scenario 1: Building a Prospect List (Detailed)

**User Query**: "Help me find 50 marketing managers at SaaS companies in San Francisco"

**Step-by-Step Execution:**

```mermaid
sequenceDiagram
    participant User
    participant Agent
    participant TableIndexing
    participant LLM
    participant LinkedInTool
    participant Database

    User->>Agent: "Find 50 marketing managers at SaaS companies in SF"
    Agent->>TableIndexing: Analyze table structure
    TableIndexing->>Database: Get column metadata
    Database-->>TableIndexing: Column data
    TableIndexing->>LLM: Generate column summaries
    LLM-->>TableIndexing: AI-generated descriptions
    TableIndexing-->>Agent: Table summary with schema

    Agent->>LLM: Plan LinkedIn search strategy
    LLM-->>Agent: Tool call: search_linkedin_profiles
    Agent->>LinkedInTool: Execute search with filters
    LinkedInTool->>Database: Create profile columns
    LinkedInTool-->>Agent: 50 profiles found and saved

    Agent->>User: "Found 50 marketing managers. Would you like me to enrich with contact data?"
```

**Expected Tool Sequence:**
1. `search_linkedin_profiles(filters=[job_title="Marketing Manager", company_type="SaaS", location="San Francisco"], page=1)`
2. `upsert_linkedin_person_profile_column_from_url(column_name="LinkedIn Profile", linkedin_profile_url="{{profile_url}}")`
3. `run_column(column_id="profile_column_id", count=50, wait_results=False)`

### Scenario 2: Email Discovery and Outreach Creation

**User Query**: "Find work emails for my prospects and create personalized outreach messages"

```mermaid
sequenceDiagram
    participant User
    participant Agent
    participant EmailTool
    participant ResearchTool
    participant CopywriterTool
    participant Database

    User->>Agent: "Find emails and create personalized messages"
    Agent->>EmailTool: Create work email column
    EmailTool->>Database: Setup email discovery column
    EmailTool-->>Agent: Email column created

    Agent->>User: "Confirm: Run email discovery for X prospects?"
    User-->>Agent: "Yes, proceed"

    Agent->>EmailTool: Execute email discovery
    EmailTool-->>Agent: Email discovery running

    Agent->>ResearchTool: Create research column
    ResearchTool->>Database: Setup research column
    Agent->>CopywriterTool: Create message column
    CopywriterTool->>Database: Setup copywriter column

    Agent->>User: "Email discovery complete. Research and messages ready to generate."
```

**Expected Tool Sequence:**
1. `upsert_work_email_column(column_name="Work Email", full_name="{{LinkedIn Profile.cell_details.full_name}}", company_domain="{{LinkedIn Company.cell_details.website}}")`
2. `run_column(column_id="email_column_id", count=50, wait_results=True)`
3. `upsert_bond_ai_researcher_column(column_name="Research Insights", linkedin_profile_url="{{LinkedIn Profile.cell_value}}")`
4. `upsert_ai_message_copywriter(column_name="Personalized Message", prompt="Write a personalized LinkedIn message...")`

### Scenario 3: Data Filtering and Analysis

**User Query**: "Show me only prospects from companies with 100+ employees who haven't been contacted"

```mermaid
sequenceDiagram
    participant User
    participant Agent
    participant FilterTool
    participant DataTool
    participant Database

    User->>Agent: "Filter for large companies, not contacted"
    Agent->>FilterTool: Read current filters
    FilterTool->>Database: Get table filters
    FilterTool-->>Agent: Current filter state

    Agent->>FilterTool: Update filters
    FilterTool->>Database: Apply new filter criteria
    FilterTool-->>Agent: Filters updated

    Agent->>DataTool: Read filtered data
    DataTool->>Database: Query with filters
    DataTool-->>Agent: Filtered dataset summary

    Agent->>User: "Showing X prospects from companies 100+ employees, not contacted"
```

**Expected Tool Sequence:**
1. `read_user_view_table_filters()`
2. `update_user_view_table_filters_tool(filters=FilterGroup(operator="AND", rules=[...]))`
3. `read_table_data(max_rows=10, summarize=True)`

## Advanced Features

### Smart Column Execution with Confirmation

The agent implements intelligent confirmation for resource-intensive operations:

```python
# Example confirmation flow for run_column
if tool_call["name"] == "run_column":
    confirmation_message = {
        "column_name": column_name,
        "column_id": args.get("column_id"),
        "rows_count": count,
        "message": f"Do you want to run the column '{column_name}' for {rows_text}?"
    }
    user_response = interrupt(confirmation_message)
```

### Injection Path System

The agent uses a sophisticated injection path system for data references:

**Basic Syntax:**
- `{{Column Name.cell_value}}` - Direct cell value
- `{{Column Name.cell_details.field_name}}` - Nested field access
- `{{Column Name.cell_details.array_field.0.property}}` - Array element access

**Examples:**
- `{{LinkedIn Profile.cell_details.full_name}}` - Person's full name
- `{{LinkedIn Company.cell_details.website}}` - Company website
- `{{LinkedIn Profile.cell_details.experiences.0.company}}` - First job company

### Error Handling Patterns

**Tool Execution Errors:**
```python
try:
    tool_result = tool.invoke(tool_call["args"])
    outputs.append(ToolMessage(content=str(tool_result), ...))
except Exception as e:
    error_message = f"Error executing tool '{tool_call['name']}': {str(e)}"
    outputs.append(ToolMessage(content=error_message, ...))
```

**Model Call Fallbacks:**
```python
try:
    response = model.invoke(messages, config)
    return {"messages": [response]}
except Exception as e:
    fallback_messages = [SystemMessage(f"Error: {str(e)}"), *cleaned_messages[-3:]]
    fallback_response = model.invoke(fallback_messages, config)
    return {"messages": [fallback_response]}
```

## Key Features

### Intelligent Data Access
- 95% of operations use cached table summary instead of direct data queries
- Automatic alignment with user's current table view (filters, sorts, searches)
- Token-efficient summarization for large datasets

### Interactive Confirmations
- User approval required for column execution operations
- Clear communication of action scope (row counts, column names)
- Graceful handling of user cancellations

### Error Resilience
- Comprehensive error handling at every layer
- Automatic retries with fallback strategies
- Detailed error reporting for debugging

### Mode Flexibility
- **Tool Mode**: Full agent capabilities with tool access
- **Chat Mode**: Conversational guidance without tool execution
- Dynamic mode switching based on user needs

## Performance Optimizations

### Table Summary Caching
- Column descriptions cached in database to avoid repeated AI analysis
- Fresh schema combined with stored summaries for optimal performance
- Only processes columns lacking descriptions

### Efficient Data Queries
- Summarization preferred over raw data retrieval
- Targeted column selection when specific data needed
- Respect for user's current view filters and sorts

### Stream Writing
- Real-time status updates for long-running operations
- User feedback during tool execution
- Progress tracking for multi-step workflows

## Implementation Details

### LangGraph Configuration

```python
def create_graph():
    workflow = StateGraph(AgentState, config_schema=Configuration)

    # Node definitions
    workflow.add_node("table_indexing", table_indexing_node)
    workflow.add_node("agent", call_model)
    workflow.add_node("tools", tool_node)

    # Flow definition
    workflow.set_entry_point("table_indexing")
    workflow.add_edge("table_indexing", "agent")
    workflow.add_conditional_edges("agent", should_continue, {
        "continue": "tools",
        "end": END,
    })
    workflow.add_edge("tools", "agent")

    return workflow.compile()
```

### State Reducers

**Message Aggregation:**
```python
messages: Annotated[Sequence[BaseMessage], add_messages]
```
- Automatically combines new messages with conversation history
- Maintains chronological order
- Handles different message types (Human, AI, Tool, System)

**Table Summary Preservation:**
```python
def preserve_table_summary(left: Optional[str], right: Optional[str]) -> Optional[str]:
    if right is not None:
        return right
    return left
```

### Tool Registration Pattern

```python
tools = [search, scrape_website, upsert_linkedin_person_profile_column_from_url, ...]
tools_by_name = {tool.name: tool for tool in tools}
```

## Best Practices and Guidelines

### Tool Development
1. **Consistent Error Handling**: All tools return `Tuple[Optional[str], Optional[str]]`
2. **Stream Writing**: Provide user feedback for long operations
3. **Validation**: Use Pydantic models for input validation
4. **Configuration Injection**: Access table_id and settings via `RunnableConfig`

### Prompt Engineering
1. **Context Awareness**: Include table summary and current filters
2. **Mode Differentiation**: Separate instructions for chat vs tool mode
3. **Selected Data Context**: Highlight user-selected rows/columns
4. **Error Recovery**: Provide fallback prompts for error scenarios

### State Management
1. **Minimal State**: Only store essential information in state
2. **Reducer Functions**: Use appropriate reducers for state updates
3. **Type Safety**: Leverage TypedDict for state structure
4. **Immutability**: Treat state as immutable, return updates

### Performance Considerations
1. **Lazy Loading**: Load data only when needed
2. **Caching Strategy**: Cache expensive operations (table summaries)
3. **Batch Operations**: Group related operations when possible
4. **Resource Management**: Clean up resources after tool execution

## Security and Data Protection

### Sensitive Data Handling
- Exclude `run_status` fields from AI analysis
- Validate injection sequences before execution
- Sanitize user inputs in tool parameters
- Implement proper access controls for table operations

### API Key Management
- Environment variable configuration for external services
- Graceful degradation when services unavailable
- Error messages that don't expose sensitive information

## Monitoring and Debugging

### Logging Strategy
- Comprehensive error logging with context
- Tool execution tracking
- Performance metrics for optimization
- User interaction patterns

### Debug Information
- State transitions logged
- Tool call parameters and results
- Model response analysis
- Error stack traces with sanitization

## Extension Points

### Adding New Tools
1. Create tool function with proper type annotations
2. Add to tools list in `tools.py`
3. Update documentation and prompts
4. Implement proper error handling and validation

### Custom Node Types
1. Define node function with `(state: AgentState, config: RunnableConfig)` signature
2. Add to workflow with `workflow.add_node()`
3. Define appropriate edges and conditions
4. Test state transitions thoroughly

### Integration Patterns
1. **Database Integration**: Use `agent_db.py` functions
2. **External APIs**: Implement with proper error handling
3. **AI Model Integration**: Use utility functions from `utils.py`
4. **Validation Services**: Leverage existing validation patterns

This documentation provides a comprehensive technical overview of the Outbond AI Assistant implementation, covering architecture, workflows, tools, and best practices for development and maintenance.
