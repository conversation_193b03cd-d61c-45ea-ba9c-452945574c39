# Call Model Node Documentation

## Overview

The `call_model` node is the core reasoning component of the Outbound AI Assistant's ReAct agent architecture. It serves as the primary interface between user interactions and the language model, orchestrating tool usage and managing conversation flow.

## Architecture

### Core Responsibilities
- **Language Model Interaction**: Loads and configures LLM based on system configuration
- **Tool Orchestration**: Binds 15 specialized tools to the model (except in chat mode)
- **Context Management**: Constructs comprehensive system prompts with dynamic table context
- **State Processing**: Manages conversation history, selected items, and table summaries
- **Error Handling**: Provides graceful fallbacks and error recovery

### Operational Modes

#### Chat Mode (`state["mode"] == "chat"`)
- **Purpose**: Conversational responses only
- **Tool Access**: None (tools are not bound to model)
- **Use Cases**: Q&A, guidance, explanations, general assistance
- **System Prompt Addition**: Includes chat mode instructions

#### Tool Mode (Default)
- **Purpose**: Action-capable responses with tool execution
- **Tool Access**: Full access to all 15 specialized tools
- **Use Cases**: Data manipulation, automation, column creation, research
- **System Prompt**: Standard prompt with tool descriptions

## Tools Inventory

### 1. Web Research Tools

#### `search`
- **Purpose**: General web search using Tavily search engine
- **Parameters**:
  - `query` (str): Search query string
  - `max_results` (int, default=5): Maximum number of results
- **Returns**: List of search results with URLs, titles, and content
- **Use Cases**: Current events research, general information gathering

#### `scrape_website`
- **Purpose**: Extract clean content from web pages
- **Parameters**:
  - `url` (str): Website URL to scrape
- **Returns**: Clean markdown content from webpage
- **Use Cases**: Detailed information extraction from specific websites

### 2. Data Operations Tools

#### `read_table_data`
- **Purpose**: Fetch and analyze table data with advanced filtering
- **Parameters**:
  - `max_rows` (int): Maximum rows to return
  - `filters` (FilterGroup, optional): Filtering conditions
  - `search` (str, optional): Search query
  - `sorts` (List[Sort], optional): Sorting configuration
  - `column_ids` (List[int], optional): Specific columns to include
  - `summarize` (bool, default=True): Whether to include data summary
- **Returns**: Tuple of (table_data, summary)
- **Use Cases**: Reading current table contents, applying filters, data analysis

#### `run_column`
- **Purpose**: Execute column processing with user confirmation
- **Parameters**:
  - `column_id` (str): ID of column to execute
  - `column_name` (str): Name of column for confirmation
  - `count` (int, default=1): Number of rows to process
  - `row_id` (int, default=1): Specific row to monitor
  - `wait_results` (bool, default=False): Wait for completion
- **Returns**: Tuple of (execution_results, error_message)
- **Use Cases**: Triggering smart column processing, data enrichment
- **Special Feature**: Requires user confirmation before execution

### 3. LinkedIn Enrichment Tools

#### `upsert_linkedin_person_profile_column_from_url`
- **Purpose**: Create/update LinkedIn profile enrichment columns
- **Parameters**:
  - `column_name` (str): Descriptive column name
  - `linkedin_profile_url` (str): Injection path to LinkedIn URL
  - `column_id` (int, optional): Existing column ID for updates
- **Returns**: Tuple of (success_message, error_message)
- **Use Cases**: Profile data enrichment from LinkedIn URLs

#### `upsert_linkedin_company_profile_column_from_url`
- **Purpose**: Create/update LinkedIn company enrichment columns
- **Parameters**:
  - `column_name` (str): Descriptive column name
  - `linkedin_company_url` (str): Injection path to company URL
  - `column_id` (int, optional): Existing column ID for updates
- **Returns**: Tuple of (success_message, error_message)
- **Use Cases**: Company data enrichment from LinkedIn URLs

#### `search_linkedin_profiles`
- **Purpose**: Search LinkedIn profiles with advanced filters
- **Parameters**:
  - `filters` (List[LinkedInFilterType]): Search criteria
  - `page` (int, default=1): Results page number
- **Returns**: Tuple of (search_results, error_message)
- **Use Cases**: Building prospect lists, finding target profiles

### 4. Contact Information Tools

#### `upsert_phone_number_column`
- **Purpose**: Create/update phone number enrichment columns
- **Parameters**:
  - `column_name` (str): Descriptive column name
  - `linkedin_profile_url` (str): Injection path to LinkedIn profile
  - `column_id` (int, optional): Existing column ID for updates
- **Returns**: Tuple of (success_message, error_message)
- **Use Cases**: Phone number discovery from LinkedIn profiles

#### `upsert_work_email_column`
- **Purpose**: Create/update work email enrichment columns
- **Parameters**:
  - `column_name` (str): Descriptive column name
  - `full_name` (str): Injection path to full name
  - `company_domain` (str): Injection path to company domain
  - `column_id` (int, optional): Existing column ID for updates
- **Returns**: Tuple of (success_message, error_message)
- **Use Cases**: Work email discovery using name and company domain

### 5. Content Generation Tools

#### `upsert_text_column`
- **Purpose**: Create/update text or formula columns
- **Parameters**:
  - `column_name` (str): Descriptive column name
  - `text_formula` (str): Static text or formula combining columns
  - `column_id` (int, optional): Existing column ID for updates
- **Returns**: Tuple of (success_message, error_message)
- **Use Cases**: Static text columns, formula-based content combination

#### `upsert_ai_text_column`
- **Purpose**: Create/update AI-powered text generation columns
- **Parameters**:
  - `column_name` (str): Descriptive column name
  - `prompt` (str): AI prompt template with injection paths
  - `required_fields` (List[str], default=[]): Required column references
  - `run_condition` (str, optional): Conditional execution logic
  - `column_id` (int, optional): Existing column ID for updates
- **Returns**: Tuple of (success_message, error_message)
- **Use Cases**: AI-generated content based on table data

#### `upsert_bond_ai_researcher_column`
- **Purpose**: Create/update AI research columns
- **Parameters**:
  - `column_name` (str): Descriptive column name
  - `prompt` (str): Research prompt template
  - `required_fields` (List[str], default=[]): Required column references
  - `run_condition` (str, optional): Conditional execution logic
  - `column_id` (int, optional): Existing column ID for updates
- **Returns**: Tuple of (success_message, error_message)
- **Use Cases**: Online research based on table data

#### `upsert_ai_message_copywriter`
- **Purpose**: Create/update AI message generation columns
- **Parameters**:
  - `column_name` (str): Descriptive column name
  - `prompt` (str): Message prompt template
  - `required_fields` (List[str], default=[]): Required column references
  - `run_condition` (str, optional): Conditional execution logic
  - `column_id` (int, optional): Existing column ID for updates
- **Returns**: Tuple of (success_message, error_message)
- **Use Cases**: Personalized outreach message generation

### 6. Filter Management Tools

#### `read_user_view_table_filters`
- **Purpose**: Read current table filter configuration
- **Parameters**:
  - `config` (injected): System configuration
- **Returns**: Tuple of (filters_data, error_message)
- **Use Cases**: Checking current filtering settings

#### `update_user_view_table_filters_tool`
- **Purpose**: Update table filter configuration
- **Parameters**:
  - `filters` (FilterGroup, optional): New filter configuration
- **Returns**: Tuple of (updated_data, error_message)
- **Use Cases**: Modifying table view filtering

## Context Enhancement Features

### Table Summary Integration
- Comprehensive table structure awareness
- Column descriptions and data types
- Relationship mapping between columns

### Filter Context
- Current active filtering conditions
- Filter history and modifications
- Impact on data visibility

### Selection Context
- User-selected columns for focused attention
- Selected rows with summarized content
- Context-aware tool recommendations

### Temporal Context
- Current timestamp for time-sensitive operations
- Date-aware processing and scheduling
- Historical context when relevant

## Error Handling & Reliability

### Graceful Fallback Mechanisms
- Model error recovery with reduced context
- Tool execution error handling
- Network failure resilience

### Context Preservation
- Conversation history maintenance during errors
- State consistency across failures
- User experience continuity

### Monitoring & Logging
- Tool execution tracking
- Performance metrics collection
- Error pattern analysis

## Sequence Diagrams

### 1. Call Model Node - Standard Flow

The standard flow shows how the `call_model` node processes a user request, configures the model, builds context, and generates a response.

**Key Steps:**
1. **Configuration Loading**: Extract table ID, model settings, and operational parameters
2. **Mode Detection**: Determine if operating in chat or tool mode
3. **Model Setup**: Load LLM and conditionally bind tools
4. **Context Building**: Gather table summary, filters, and user selections
5. **Prompt Construction**: Create comprehensive system prompt with dynamic content
6. **Message Processing**: Clean conversation history and prepare for model
7. **Model Invocation**: Generate response with potential tool calls
8. **State Update**: Return response for further processing or user delivery

### 2. Tool Execution Flow with User Confirmation

This diagram illustrates the special handling for tools that require user confirmation, particularly the `run_column` tool which can trigger expensive operations.

**Key Features:**
- **Confirmation Mechanism**: User must approve `run_column` operations
- **Parameter Extraction**: Tool parameters are analyzed for confirmation message
- **User Interaction**: Interactive confirmation dialog with clear action description
- **Conditional Execution**: Tool only executes if user confirms
- **Graceful Cancellation**: Proper handling when user declines

### 3. Error Handling and Fallback Flow

Demonstrates the robust error handling mechanism that ensures users receive helpful responses even when the primary model invocation fails.

**Fallback Strategy:**
- **Exception Capture**: All model errors are caught and handled gracefully
- **Context Reduction**: Fallback uses reduced message history to avoid repeated errors
- **Error Communication**: Clear error information provided to fallback model
- **User Experience**: Seamless experience with helpful responses despite errors

## Technical Implementation Details

### State Management

The `call_model` node operates on the `AgentState` which contains:

```python
class AgentState(TypedDict):
    messages: Annotated[list[AnyMessage], add_messages]
    table_summary: str
    mode: Optional[str]  # "chat" or None (tool mode)
    selected_column_ids: Optional[Union[str, List[int]]]
    selected_row_ids: Optional[List[int]]
```

### Configuration Schema

The node uses `Configuration` class with:

```python
class Configuration(BaseModel):
    table_id: str
    model: str = "claude-3-5-sonnet-20241022"
    # Additional configuration parameters
```

### System Prompt Construction

The system prompt is dynamically built using:

```python
system_prompt = OUTBOND_AI_ASSISTANT_PROMPT.format(
    table_id=configuration.table_id,
    today_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
    current_filters=current_filters,
    table_summary=state["table_summary"]
)
```

### Tool Binding Logic

```python
if is_chat_mode:
    model = load_chat_model(configuration.model)
else:
    model = load_chat_model(configuration.model).bind_tools(tools)
```

## Integration Points

### LangGraph Workflow Integration

The `call_model` node integrates with the broader LangGraph workflow:

1. **Entry Point**: Receives control from `table_indexing` node
2. **Decision Point**: Uses `should_continue` to determine next action
3. **Tool Execution**: Passes control to `tool_node` when tools are called
4. **Loop Back**: Returns to `call_model` after tool execution for follow-up

### Database Integration

- **Table Data**: Reads from Supabase tables via `read_table_data`
- **Column Operations**: Creates/updates columns via various `upsert_*` tools
- **Filter Management**: Reads/writes table filters
- **Execution Tracking**: Monitors column processing status

### External Service Integration

- **Tavily Search**: Web search capabilities
- **FireCrawl**: Website content extraction
- **LinkedIn APIs**: Profile and company data enrichment
- **Email/Phone Services**: Contact information discovery

## Performance Considerations

### Message History Management

- **Thinking Block Cleaning**: Removes internal metadata for efficiency
- **Context Limitation**: Fallback uses reduced history to prevent repeated errors
- **Memory Optimization**: Efficient message serialization and storage

### Tool Execution Optimization

- **Lazy Loading**: Tools are only loaded when needed
- **Batch Operations**: Multiple tool calls processed efficiently
- **Caching**: Configuration and table data cached appropriately

### Error Recovery

- **Graceful Degradation**: System continues operating despite individual failures
- **Context Preservation**: User conversation state maintained across errors
- **Retry Logic**: Automatic retry with reduced context on failures

## Security Considerations

### Input Validation

- **Injection Prevention**: All user inputs validated before database operations
- **Parameter Sanitization**: Tool parameters cleaned and validated
- **Access Control**: Table access restricted by user permissions

### Data Protection

- **Sensitive Data Handling**: PII and sensitive information properly managed
- **Audit Logging**: All operations logged for security monitoring
- **Rate Limiting**: Protection against abuse and excessive usage

## Monitoring and Observability

### Logging

- **Tool Execution**: All tool calls logged with parameters and results
- **Error Tracking**: Comprehensive error logging with context
- **Performance Metrics**: Response times and success rates tracked

### Metrics

- **Tool Usage**: Frequency and success rates of each tool
- **Model Performance**: Response quality and error rates
- **User Interaction**: Confirmation rates and user satisfaction

### Debugging

- **State Inspection**: Full state available for debugging
- **Message Tracing**: Complete conversation history preserved
- **Tool Call Analysis**: Detailed tool execution traces
