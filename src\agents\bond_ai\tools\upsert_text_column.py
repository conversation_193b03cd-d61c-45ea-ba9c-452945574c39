"""Text column creation tool."""

from typing import <PERSON><PERSON>, <PERSON><PERSON>, Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool, InjectedToolArg
from langgraph.config import get_stream_writer
from bond_ai.configuration import Configuration
import json
import re

from ..agent_db import upsert_smart_column


@tool
def upsert_text_column(
    config: Annotated[RunnableConfig, InjectedToolArg],
    column_name: str,
    text_formula: str,
    column_id: Optional[int] = None,
) -> Tuple[Optional[str], Optional[str]]:
    """Create a new text column or update an existing one with text or combination of other column values.
    
    IMPORTANT: ALWAYS provide the column_id if you want to update/edit an existing column. Do NOT duplicate the column by NOT providing the column_id.
    
    This tool can be used in two main ways:
    1. Create OR update/edit a column with the same static text for all rows (useful for ICP descriptions for example)
    2. Create OR update/edit a formula column that combines values from other columns cells or cell details (e.g., concatenate first name and last name)
    
    Parameters:
        config: Configuration injected by the system
        column_name: A short descriptive name of the column. Must be unique per table.
        text_formula: The text content or formula to use. Can ONLY include column references as {{injection path}}. NO other formulas are allowed.
                      Example: "{{linkedin profile.cell_details.first_name}} {{linkedin profile.cell_details.last_name}}" would combine the first name and last name columns with a space between.
        column_id: Optional ID of the existing column to update/edit. If not provided, a new column will be created.
        
    Returns:
        Tuple[Optional[str], Optional[str]]: Tuple containing (success_message, error_message)
        where success_message is a confirmation if successful, None if failed
        and error_message is the error description if failed, None if successful
    """
    try:
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"Working on {column_name} column"})
        # Set up metadata
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        service_id = 15  # Formula/Text service ID
        
        # Convert any injection sequences ({{N}}) to proper HTML span tags
        def replace_injection(match):
            injection_id = match.group(1)
            return f'<span data-type="column-embed" data-id="{{{{%s}}}}"></span>' % injection_id
            
        # Replace all {{N}} patterns with the HTML tag format
        processed_formula = re.sub(r'\{\{([^}]+)\}\}', replace_injection, text_formula)
        
        
        # Wrap in paragraph tags for the final user_prompt
        user_prompt = f"<p>{processed_formula}</p>"
        
        # Set up empty inputs and providers as specified in the payload
        inputs = []
        providers = []
        
        # Set up parameters with the user prompt
        parameters = [{"user_prompt": user_prompt}]
        
        # Call the upsert_smart_column function
        response, error = upsert_smart_column(
            table_id=table_id,
            column_name=column_name,
            service_id=service_id,
            inputs=inputs,
            parameters=parameters,
            providers=providers,
            column_id=column_id
        )
        
        if error:
            return None, error
            
        # Extract column details from response
        column = response.get('column', {})
        column_id = column.get('id')
        column_type = column.get('type') 
        name = column.get('name')
        
        action = "updated" if column_id is not None else "created"
        
        # Create JSON response
        response_json = {
            "action": action,
            "column_name": name,
            "message": f"The text column was successfully {action}. Column name: '{name}' Column ID: {column_id}, Column Type: {column_type}"
        }
        
        return json.dumps(response_json), None
        
    except Exception as e:
        return None, str(e)
