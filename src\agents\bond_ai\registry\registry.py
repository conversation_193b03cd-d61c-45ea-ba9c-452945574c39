
# ============================================================================
# SIMPLE AGENT REGISTRY
# ============================================================================


from enum import Enum
from bond_ai.tools import search_linkedin_profiles, upsert_linkedin_person_profile_column_from_url, upsert_linkedin_company_profile_column_from_url
from bond_ai.tools import read_table_data

from bond_ai.tools import upsert_work_email_column, upsert_phone_number_column

from bond_ai.tools import run_column, upsert_text_column, upsert_ai_text_column, upsert_bond_ai_researcher_column, upsert_ai_message_copywriter, read_user_view_table_filters, update_user_view_table_filters_tool

from bond_ai.tools import search, scrape_website

class AgentName(Enum):
   # agent_build_prospect_list_from_db = "agent_build_prospect_list_from_db"
    agent_research = "research_agent"
    enrichment_agent = "enrichment_agent"
    content_agent = "content_agent"
    data_management_agent = "data_management_agent"
    execution_agent = "execution_agent"
  #  agent_message_copy = "agent_message_copy"
 #   agent_action_table = "agent_action_table"
 #   agent_upload_csv = "agent_upload_csv"
 #   agent_http_column = "agent_http_column"
 #   agent_webhook_column = "agent_webhook_column"
 #   agent_formula = "agent_formula"
 #   agent_clean_up_column = "agent_clean_up_column"

AGENT_TOOLS_MAP = {
    #   AgentName.agent_build_prospect_list_from_db: [
    #     read_table_data
    # ],
     AgentName.agent_research: [
        search_linkedin_profiles,                                   # LinkedIn profile discovery
        search,                                                     # Web search for research
        scrape_website                                              # Website content extraction
    ],
    AgentName.enrichment_agent: [
        upsert_linkedin_person_profile_column_from_url,             # LinkedIn person import
        upsert_linkedin_company_profile_column_from_url,            # LinkedIn company import
        upsert_phone_number_column,                                 # Phone discovery
        upsert_work_email_column                                    # Email discovery
    ],
    AgentName.content_agent: [
        upsert_text_column,                                         # Static text/formula columns
        upsert_ai_text_column,                                      # AI-generated content
        upsert_bond_ai_researcher_column,                           # Research insights
        upsert_ai_message_copywriter                                # Personalized messaging
    ],
    AgentName.data_management_agent: [
        read_table_data,                                            # Table data retrieval
        read_user_view_table_filters,                               # Filter management
        update_user_view_table_filters_tool                         # Filter updates
    ],
    AgentName.execution_agent: [  # HITL ALWAYS
       run_column
    ],

}


class AgentRegistry:
    """Simple registry for managing agent configurations."""

    def __init__(self):
        self.agents = {}
        # self.tools = {}
        self.nodes = {}  # Store pre-built node functions

    def register_agent(self, name: str, system_prompt: str, tools: list, description: str, enabled: bool = True, **kwargs):
        """Register an agent with its configuration."""
        self.agents[name] = {
            "system_prompt": system_prompt,
            "tools": tools,
            "enabled": enabled,
            "description": description,
            "type": "react_agent",  # Default type
            **kwargs
        }
        return self

    def register_node_prebuild(self, name: str, node_function, description: str, enabled: bool = True, **kwargs):
        """Register a pre-built node function."""
        self.agents[name] = {
            #"system prompt"
            "node_function": node_function,
            #"tools"
            "enabled": enabled,
            "description": description,
            "type": "custom_node",
         
            **kwargs
        }
        return self

    # def register_tool(self, name: str, tool):
    #     """Register a tool by name."""
    #     self.tools[name] = tool
    #     return self

    def get_enabled_agents(self):
        """Get all enabled agent configurations."""
        return {name: config for name, config in self.agents.items() if config.get("enabled", True)}

    def get_agent_names(self):
        """Get list of enabled agent names."""
        return list(self.get_enabled_agents().keys())

    def get_react_agents(self):
        """Get only ReAct agents (not custom nodes)."""
        return {name: config for name, config in self.get_enabled_agents().items()
                if config.get("type") == "react_agent"}

    def get_custom_nodes(self):
        """Get only custom node functions."""
        return {name: config for name, config in self.get_enabled_agents().items()
                if config.get("type") == "custom_node"}

    def enable_agent(self, name: str):
        """Enable an agent."""
        if name in self.agents:
            self.agents[name]["enabled"] = True

    def disable_agent(self, name: str):
        """Disable an agent."""
        if name in self.agents:
            self.agents[name]["enabled"] = False

# Create global registry
supervisor_agent_registry = AgentRegistry()
