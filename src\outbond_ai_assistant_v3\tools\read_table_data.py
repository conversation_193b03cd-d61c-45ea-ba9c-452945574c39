"""Read table data tool with summarization capabilities."""

from typing import Optional, Any, List, Tuple, Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool, InjectedToolArg
from langchain_core.messages import SystemMessage, HumanMessage
from langgraph.config import get_stream_writer
from agent.configuration import Configuration
import json

from .models import FilterGroup, Sort, TableSummaryOutput
from ..agent_db import get_table_data
from ..prompts import TABLE_SUMMARY_PROMPT
from ..utils import load_chat_model_non_thinking


@tool
def read_table_data(
    config: Annotated[RunnableConfig, InjectedToolArg],
    max_rows: int,
    filters: Optional[FilterGroup] = None,
    search: Optional[str] = None, 
    sorts: Optional[List[Sort]] = None,
    column_ids: Optional[List[int]] = None,
    summarize: bool = True,
) -> Tuple[Optional[Any], Optional[str]]:
    """Fetch user table data from a specified table.
    
    Args:
        table_id: The ID of the table to get data from
        max_rows: Maximum number of rows to fetch
        filters: Filtering conditions using the FilterGroup model. Always use the same filters as the user view filters.
        search: Search text to filter results
        sorts: Sort criteria using the Sort model
        column_ids: Specific column IDs to filter returned columns
        summarize: Whether to summarize the table data using LLM for token efficiency (default: True)
        
    Returns:
        Tuple containing (table_data, error) where table_data is the structured 
        data if successful, None if failed, and error is the error message 
        if failed, None if successful. When summarize=True, table_data will be
        an LLM-generated summary instead of raw data.
        
    """
    try:
        stream_writer = get_stream_writer()

        if summarize == False:
            stream_writer({"custom_tool_call": f"Reading table"})
        
        # Get the table_id from the configuration
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        
        # Convert pydantic models to dictionaries for the function call
        # Handle filters - could be FilterGroup object or already a dictionary
        if filters is not None:
            if hasattr(filters, 'model_dump'):
                filters_dict = filters.model_dump(mode='json')  # Use mode='json' to convert enums to values
            else:
                filters_dict = filters  # Already a dictionary
        else:
            filters_dict = None
            
        # Handle sorts - could be list of Sort objects or already a list of dictionaries  
        if sorts is not None:
            if all(hasattr(sort, 'model_dump') for sort in sorts):
                sorts_dict = [sort.model_dump(mode='json') for sort in sorts]  # Use mode='json' to convert enums to values
            else:
                sorts_dict = sorts  # Already a list of dictionaries
        else:
            sorts_dict = None
        
        table_data, error = get_table_data(
            table_id=table_id, 
            max_rows=max_rows,
            filters=filters_dict,
            search=search,
            sorts=sorts_dict,
            column_ids=column_ids
        )
        
        if error:
            print(f"Error from get_table_data: {error}")
            return None, error
            
        if not table_data:
            print("No table data available")
            return None, "No table data available"
        
        print(f"Got table data, summarize={summarize}")
        print(f"Table data type: {type(table_data)}")
        
        # If summarize is enabled, use LLM to create a token-efficient summary
        if summarize:
            stream_writer({"custom_tool_call": f"Indexing the table"})
            print("Starting summarization...")
            try:
                model = load_chat_model_non_thinking("openai/gpt-4o-mini").with_structured_output(TableSummaryOutput, method="function_calling")
                
                # Convert table data to JSON string for the prompt
                table_data_str = json.dumps(table_data, indent=2, default=str)
                print(f"Table data JSON length: {len(table_data_str)}")
                
                # Create two separate messages - one for the prompt, one for the data
                prompt_message = SystemMessage(TABLE_SUMMARY_PROMPT)
                data_message = HumanMessage(table_data_str)
                
                # Get the structured summary from the model using both messages
                print("Calling model for summarization...")
                summary_response = model.invoke([prompt_message, data_message], config)
                
                print("Model response received, returning structured summary")
                # Return the structured TableSummaryOutput object
                return summary_response, None
                    
            except Exception as summary_error:
                print(f"Summarization failed: {summary_error}")
                # If summarization fails, return raw data with a warning
                return table_data, f"Warning: Summarization failed ({str(summary_error)}), returning raw data"
        
        print("Summarize is False, returning raw data")
        # Return raw data when summarize is False
        return table_data, None
            
    except Exception as e:
        print(f"Function error: {e}")
        return None, str(e)
