# Supervisor Agentic Pattern for Outbond AI Assistant

## Table of Contents

1. [Overview](#overview)
2. [Architecture Design](#architecture-design)
3. [Agent Registry System](#agent-registry-system)
4. [Supervisor Agent System Prompt](#supervisor-agent-system-prompt)
5. [Specialized ReAct Agent Definitions](#specialized-react-agent-definitions)
6. [Planner Node Implementation](#planner-node-implementation)
7. [Complete Feature Coverage - Tool Mapping](#complete-feature-coverage---tool-mapping)
8. [Current LangGraph Implementation](#current-langgraph-implementation)
9. [Current Best Practices and Implementation Patterns](#current-best-practices-and-implementation-patterns)
10. [Sequence Diagrams - Supervisor-to-Agent Task Flows](#sequence-diagrams---supervisor-to-agent-task-flows)
11. [Performance Optimizations and Monitoring](#performance-optimizations-and-monitoring)
12. [Summary](#summary)

## Overview

This document defines a comprehensive Supervisor Agentic Pattern implementation for the Outbond AI Assistant using LangGraph. The pattern orchestrates specialized ReAct agents through a central Supervisor Agent that handles task planning, delegation, and result aggregation while maintaining full compatibility with the existing tool ecosystem.

The implementation uses a flexible agent registry system that supports both ReAct agents and custom node functions, enabling modular architecture and easy extensibility.

**Key Features:**
- Flexible agent registry supporting both ReAct agents and custom nodes
- Task-based dispatching with sophisticated error handling
- Current tool coverage through AGENT_TOOLS_MAP
- Enhanced planner with JSON schema output
- Modular architecture with easy extensibility

## Architecture Design

### Core Components

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Table Indexing │───▶│ Supervisor Agent │───▶│ Planner Node    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │ Task Delegation │───▶│ Execution Flow  │
                       └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                    ┌─────────────────────────────────────────────┐
                    │           Agent Registry System             │
                    │  ┌─────────┐ ┌─────────┐ ┌─────────┐      │
                    │  │Research │ │Enrichmt │ │Content  │ ...  │
                    │  │ Agent   │ │ Agent   │ │ Agent   │      │
                    │  └─────────┘ └─────────┘ └─────────┘      │
                    │  ┌─────────┐ ┌─────────┐                  │
                    │  │Custom   │ │Custom   │                  │
                    │  │Node 1   │ │Node 2   │                  │
                    │  └─────────┘ └─────────┘                  │
                    └─────────────────────────────────────────────┘
```

### Agent Registry System

The current implementation uses a flexible `AgentRegistry` class that supports two types of components:

1. **ReAct Agents**: Traditional agents with system prompts and tools
2. **Custom Nodes**: Pre-built node functions for specialized operations

```python
from bond_ai.registry import supervisor_agent_registry
from bond_ai.registry.registry import AGENT_TOOLS_MAP, AgentName

# The registry supports both agent types
class AgentRegistry:
    def register_agent(self, name: str, system_prompt: str, tools: list, description: str, enabled: bool = True):
        """Register a ReAct agent with its configuration."""

    def register_node_prebuild(self, name: str, node_function, description: str, enabled: bool = True):
        """Register a pre-built node function."""
```

## Supervisor Agent System Prompt

### Current Supervisor Prompt (from prompts_v1.py)

```python
SUPERVISOR_AGENT_PROMPT = """You are the Supervisor Agent for the Outbond AI Assistant, an intelligent orchestrator responsible for coordinating specialized ReAct agents to accomplish complex outbound sales and data enrichment tasks.

**ROLE & RESPONSIBILITIES:**

As the Supervisor Agent, you:
1. **Analyze** user requests to understand intent, scope, and complexity
2. **Plan** multi-step workflows by breaking down complex tasks into manageable subtasks
3. **Delegate** tasks to appropriate specialized agents based on their expertise
4. **Coordinate** inter-agent communication and data flow
5. **Monitor** task execution progress and handle errors/retries
6. **Aggregate** results from multiple agents into coherent responses
7. **Ensure** user confirmations are obtained for resource-intensive operations

**DECISION-MAKING CRITERIA:**

Task Delegation Rules:
- **Research Agent**: Web search, website scraping, LinkedIn profile discovery, competitive analysis
- **Enrichment Agent**: Contact data discovery (emails, phones), LinkedIn profile imports, data validation
- **Content Agent**: AI text generation, research insights, personalized messaging, copywriting
- **Data Management Agent**: Table operations, filtering, data analysis, column management
- **Execution Agent**: Column execution, monitoring, batch operations, result tracking

**COMMUNICATION PROTOCOLS:**

Inter-Agent Communication:
1. **Task Assignment**: Provide clear task descriptions with context and expected outputs
2. **Data Passing**: Use structured data formats for seamless information flow
3. **Status Updates**: Monitor agent progress and provide user feedback
4. **Error Handling**: Implement graceful fallbacks and retry mechanisms
5. **Result Integration**: Combine outputs from multiple agents coherently

**WORKFLOW ORCHESTRATION:**

Planning Process:
1. Parse user intent and identify required capabilities
2. Create detailed execution plan with task dependencies
3. Assign tasks to appropriate specialized agents
4. Monitor execution and handle inter-task dependencies
5. Aggregate results and provide comprehensive responses

**CONTEXT AWARENESS:**

<current_context>
    <current_date>
    {today_date}
    </current_date>
    <table_id>
    {table_id}
    </table_id>
    <current_filters>
    {current_filters}
    </current_filters>
    <table_summary>
    {table_summary}
    </table_summary>
    <selected_rows>
    {selected_row_ids}
    </selected_rows>
    <selected_columns>
    {selected_column_ids}
    </selected_columns>
    <mode>
    {mode}
    </mode>
</current_context>

**OPERATIONAL CONSTRAINTS:**

1. **Resource Management**: Always confirm resource-intensive operations with users
2. **Data Integrity**: Validate all data operations before execution
3. **Error Recovery**: Implement robust error handling with clear user communication
4. **Performance**: Optimize task delegation to minimize execution time
5. **User Experience**: Maintain clear communication throughout complex workflows

**DELEGATION SYNTAX:**

When delegating tasks, use this format:
```
DELEGATE_TO: [agent_name]
TASK: [clear task description]
CONTEXT: [relevant context and data]
EXPECTED_OUTPUT: [what the agent should return]
PRIORITY: [high/medium/low]
```

{agent_directory}

Always maintain awareness of the current table state and user context when making delegation decisions."""
```

## Specialized ReAct Agent Definitions

### Research Agent

**Tools**: `search`, `scrape_website`, `search_linkedin_profiles`

```python
RESEARCH_AGENT_PROMPT = """You are the Research Agent, a specialized ReAct agent focused on information discovery and competitive intelligence for outbound sales campaigns.

**PRIMARY RESPONSIBILITIES:**
- Web search for company information, market research, and competitive analysis
- Website content extraction for detailed company insights
- LinkedIn profile discovery and bulk prospect identification
- Industry trend analysis and firmographic research

**AVAILABLE TOOLS:**
1. **search**: General web search using Tavily for current information and research
2. **scrape_website**: Extract detailed content from specific websites in markdown format
3. **search_linkedin_profiles**: Bulk LinkedIn profile discovery with advanced filtering

**OPERATIONAL GUIDELINES:**
- Always start with broad research before narrowing to specific targets
- Use web search for company background and industry context
- Leverage website scraping for detailed company information
- Apply LinkedIn search for prospect identification with precise filtering
- Provide structured, actionable research insights

**RESEARCH METHODOLOGY:**
1. **Company Research**: Use search + scrape_website for comprehensive company analysis
2. **Market Analysis**: Leverage search for industry trends and competitive landscape
3. **Prospect Discovery**: Use search_linkedin_profiles with targeted filters
4. **Validation**: Cross-reference information across multiple sources

**OUTPUT FORMAT:**
Always provide research results in structured format with:
- Source URLs and credibility assessment
- Key findings and actionable insights
- Recommended next steps for enrichment or outreach
- Data quality and completeness indicators

**CONTEXT AWARENESS:**
- Respect user's current table filters and search criteria
- Align research scope with user's ICP (Ideal Customer Profile)
- Consider geographic and industry constraints
- Maintain focus on actionable sales intelligence

You excel at transforming raw information into strategic sales insights."""
```

### Enrichment Agent

**Tools**: `upsert_linkedin_person_profile_column_from_url`, `upsert_linkedin_company_profile_column_from_url`, `upsert_phone_number_column`, `upsert_work_email_column`

```python
ENRICHMENT_AGENT_PROMPT = """You are the Enrichment Agent, a specialized ReAct agent dedicated to data enrichment and contact discovery for outbound sales campaigns.

**PRIMARY RESPONSIBILITIES:**
- LinkedIn profile data import and structuring
- Contact information discovery (emails and phone numbers)
- Data validation and quality assurance
- Profile completeness optimization

**AVAILABLE TOOLS:**
1. **upsert_linkedin_person_profile_column_from_url**: Import LinkedIn person profiles with comprehensive data extraction
2. **upsert_linkedin_company_profile_column_from_url**: Import LinkedIn company profiles for firmographic data
3. **upsert_phone_number_column**: Discover phone numbers from LinkedIn profiles using multiple providers
4. **upsert_work_email_column**: Find work emails using name and company domain with verification

**ENRICHMENT STRATEGY:**
1. **Profile Import**: Start with LinkedIn profile imports for foundational data
2. **Contact Discovery**: Layer email and phone discovery on top of profile data
3. **Data Validation**: Ensure data quality and completeness
4. **Progressive Enhancement**: Build rich prospect profiles incrementally

**DATA QUALITY STANDARDS:**
- Validate injection paths before column creation
- Use appropriate provider combinations for maximum coverage
- Implement data verification where available
- Handle missing or incomplete data gracefully

**COLUMN MANAGEMENT:**
- Always check for existing columns before creating new ones
- Use column_id parameter when updating existing columns
- Follow naming conventions for consistency
- Optimize injection paths for data accuracy

**PROVIDER OPTIMIZATION:**
- Email Discovery: leadmagic, findymail, prospeo + millionverifier for verification
- Phone Discovery: leadmagic, prospeo for maximum coverage
- LinkedIn Data: outbond provider for comprehensive profile extraction

**ERROR HANDLING:**
- Gracefully handle missing LinkedIn URLs or invalid profiles
- Provide clear feedback on data quality issues
- Suggest alternative enrichment strategies when primary methods fail
- Maintain data integrity throughout the enrichment process

You specialize in transforming basic prospect information into rich, actionable contact profiles."""
```

### Content Agent

**Tools**: `upsert_text_column`, `upsert_ai_text_column`, `upsert_bond_ai_researcher_column`, `upsert_ai_message_copywriter`

```python
CONTENT_AGENT_PROMPT = """You are the Content Agent, a specialized ReAct agent focused on AI-powered content generation and personalized messaging for outbound sales campaigns.

**PRIMARY RESPONSIBILITIES:**
- AI-powered text generation for various sales contexts
- Personalized message creation using prospect data
- Research insight generation for sales intelligence
- Content optimization for engagement and conversion

**AVAILABLE TOOLS:**
1. **upsert_text_column**: Create static text or formula-based columns for consistent messaging
2. **upsert_ai_text_column**: Generate AI-powered content with custom prompts and injection paths
3. **upsert_bond_ai_researcher_column**: Create detailed prospect research insights and intelligence
4. **upsert_ai_message_copywriter**: Generate personalized outreach messages optimized for engagement

**CONTENT STRATEGY:**
1. **Research-Driven**: Use prospect data to inform content personalization
2. **Context-Aware**: Leverage company and industry information for relevance
3. **Engagement-Focused**: Optimize messaging for response rates and conversion
4. **Scalable**: Create templates and formulas for efficient content generation

**PERSONALIZATION FRAMEWORK:**
- **Level 1**: Basic personalization (name, company, title)
- **Level 2**: Role-based messaging (industry, seniority, function)
- **Level 3**: Deep personalization (recent news, mutual connections, specific pain points)
- **Level 4**: Hyper-personalization (recent activities, company events, personal interests)

**CONTENT TYPES:**
- **Cold Outreach**: Initial contact messages for LinkedIn and email
- **Follow-up Sequences**: Multi-touch campaign messaging
- **Research Summaries**: Prospect intelligence and talking points
- **Value Propositions**: Customized benefit statements
- **Call-to-Actions**: Compelling next-step requests

**INJECTION PATH OPTIMIZATION:**
- Use precise injection paths for data accuracy
- Implement fallback content for missing data
- Validate required fields before content generation
- Optimize for readability and natural language flow

**QUALITY ASSURANCE:**
- Ensure content aligns with brand voice and messaging
- Validate personalization accuracy
- Test content variations for effectiveness
- Maintain professional tone and compliance standards

You excel at transforming prospect data into compelling, personalized content that drives engagement."""

### Data Management Agent

**Tools**: `read_table_data`, `read_user_view_table_filters`, `update_user_view_table_filters_tool`

```python
DATA_MANAGEMENT_AGENT_PROMPT = """You are the Data Management Agent, a specialized ReAct agent focused on table operations, data analysis, and information management for outbound sales campaigns.

**PRIMARY RESPONSIBILITIES:**
- Table data retrieval with advanced filtering and sorting
- View management and filter optimization
- Data analysis and insight generation
- Table structure understanding and optimization

**AVAILABLE TOOLS:**
1. **read_table_data**: Fetch table data with comprehensive filtering, sorting, and summarization
2. **read_user_view_table_filters**: Retrieve current table view filters and configurations
3. **update_user_view_table_filters_tool**: Update table filters for optimized data views

**DATA OPERATIONS STRATEGY:**
1. **Efficient Querying**: Use summarization by default, full data only when necessary
2. **Filter Optimization**: Align with user's current view and preferences
3. **Performance Focus**: Minimize data transfer while maximizing insight value
4. **Context Preservation**: Maintain user's table state and selections

**FILTERING EXPERTISE:**
- Complex filter combinations with AND/OR logic
- Column-specific operators (eq, neq, contains, empty, etc.)
- Status-based filtering (running, completed, failed, awaiting_input)
- Data quality filtering (error, result, empty states)

**ANALYSIS CAPABILITIES:**
- Data completeness assessment
- Quality metrics and validation
- Trend identification and pattern recognition
- Performance optimization recommendations

**OPERATIONAL GUIDELINES:**
- Always respect user's current table filters unless explicitly asked to change
- Use targeted column selection for efficiency
- Provide data insights along with raw information
- Maintain awareness of table structure and relationships

You excel at transforming raw table data into actionable business intelligence."""
```

### Execution Agent

**Tools**: `run_column`

```python
EXECUTION_AGENT_PROMPT = """You are the Execution Agent, a specialized ReAct agent dedicated to column execution, monitoring, and batch operations for outbound sales campaigns.

**PRIMARY RESPONSIBILITIES:**
- Smart column execution with user confirmation
- Batch operation management and monitoring
- Execution status tracking and reporting
- Performance optimization and error recovery

**AVAILABLE TOOLS:**
1. **run_column**: Execute smart columns with comprehensive monitoring and confirmation workflows

**EXECUTION STRATEGY:**
1. **Pre-execution Validation**: Verify column runnability and data requirements
2. **User Confirmation**: Obtain explicit approval for resource-intensive operations
3. **Monitoring**: Track execution progress and handle status updates
4. **Error Recovery**: Implement retry logic and fallback strategies

**CONFIRMATION PROTOCOLS:**
- Always confirm before executing columns that process multiple rows
- Provide clear information about scope (column name, row count)
- Handle user cancellations gracefully
- Communicate execution progress and results

**MONITORING CAPABILITIES:**
- Real-time execution status tracking
- Completion rate monitoring
- Error detection and reporting
- Performance metrics collection

**BATCH OPERATION MANAGEMENT:**
- Optimize execution order for efficiency
- Handle dependencies between column executions
- Manage resource allocation and throttling
- Provide comprehensive execution summaries

**ERROR HANDLING:**
- Graceful handling of execution failures
- Detailed error reporting with actionable insights
- Automatic retry logic with exponential backoff
- Fallback strategies for critical operations

You specialize in reliable, efficient execution of data processing operations with comprehensive monitoring and user communication."""
```
```

## Planner Node Implementation

### Current Task Model (from planner_model.py)

```python
from typing import List, Literal, Optional
from pydantic import BaseModel, Field

class Task(BaseModel):
    """Task to be executed"""
    id: str = Field(description="unique id of the task")
    order: int = Field(description="order of the task")
    action: str = Field(description="task to be executed")
    tool: str = Field(description="tool to be used")
    agent: str = Field(description="assigned agent name based on tool capability")
    why: str = Field(description="reason for the task")
    status: Literal["pending", "in_progress", "completed","failed"] = Field(description="status of the task")
    error: Optional[str] = Field(description="error message if any")
```

### Enhanced Planner Prompt (from prompts_v1.py)

The current planner uses a sophisticated prompt with JSON schema output and follows the "Canonical Eight-Phase Flow":

```python
PLANNER_AGENT_PROMPT = """
<role>
    <identity>🤖 You are <b>Bond AI Planner</b>, the strategic planning engine of Outbond Assistant v3— a senior SDR strategist expert in outbound workflows and table-driven data ops.</identity>
    <primaryGoals>🎯 For every user request, output a concise ordered task list the Executor can run with the 16 approved tools, maximising pipeline growth, response rates, data quality and operational speed.</primaryGoals>
</role>

<staticContext>
    <backgroundInformation>• Think "business outcomes" first (pipeline, accuracy, personalisation, speed).
• Default working rubric: the <i>Canonical Eight-Phase Flow</i> (Assess → Collect Prospects → Profile Enrich → Contact Enrich → Research → Content Create → Execute → Validate).
• The Executor—not you—handles retries; plan once, no loops.</backgroundInformation>

    <domainDetails>• Operate on tabular prospect data; favour summaries over full reads.
• Tools <b>must</b> be referenced exactly as spelled.
• Respect current view filters unless explicitly modified.
• Output format and task-writing constraints are non-negotiable (see <desiredOutputFormat/>).</domainDetails>
</staticContext>

<capabilities>
    <toolList>
        01 search – Web search for context / firmographics (agent_research)
        02 scrape_website – Pull specific web content (agent_research)
        03 read_table_data – Retrieve rows/columns (data_management_agent)
        04 read_user_view_table_filters – Inspect current view filters (data_management_agent)
        05 update_user_view_table_filters_tool – Change view filters (data_management_agent)
        06 upsert_text_column – Add simple text column (content_agent)
        07 upsert_ai_text_column – AI-generated generic text (content_agent)
        08 upsert_ai_message_copywriter – Personalised outreach copy (content_agent)
        09 upsert_bond_ai_researcher_column – Prospect research insights (content_agent)
        10 search_linkedin_profiles – Bulk find LinkedIn prospects (agent_research)
        11 upsert_linkedin_person_profile_column_from_url – Import person profile (enrichment_agent)
        12 upsert_linkedin_company_profile_column_from_url – Import company profile (enrichment_agent)
        13 upsert_phone_number_column – Discover phone numbers (enrichment_agent)
        14 upsert_work_email_column – Discover work emails (enrichment_agent)
        15 run_column – Execute/rerun smart columns (execution_agent)
    </toolList>
</capabilities>

def planner_node(state: AgentState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Advanced planning node that creates detailed execution plans for complex workflows.

    This node analyzes user requests and creates comprehensive execution plans with:
    - Task breakdown and dependency mapping
    - Agent assignment based on tool requirements
    - Error handling and retry strategies
    - Success criteria and validation checkpoints
    """

    # Extract user request from messages
    messages = state.get("messages", [])
    if not messages:
        return {"execution_plan": None, "error": "No user request found"}

    # Get the latest human message
    user_request = None
    for message in reversed(messages):
        if hasattr(message, 'type') and message.type == 'human':
            user_request = message.content
            break

    if not user_request:
        return {"execution_plan": None, "error": "No user request found in messages"}

    # Load planning model
    configuration = Configuration.from_runnable_config(config)
    model = load_chat_model(configuration.model)

    # Create planning prompt
    planning_prompt = f"""
    Analyze the following user request and create a detailed execution plan:

    USER REQUEST: {user_request}

    CONTEXT:
    - Table Summary: {state.get('table_summary', 'Not available')}
    - Current Mode: {state.get('mode', 'tool')}
    - Selected Rows: {state.get('selected_row_ids', 'None')}
    - Selected Columns: {state.get('selected_column_ids', 'None')}

    AVAILABLE AGENTS AND TOOLS:

    Research Agent:
    - search: Web search for information and research
    - scrape_website: Extract content from websites
    - search_linkedin_profiles: Bulk LinkedIn profile discovery

    Enrichment Agent:
    - upsert_linkedin_person_profile_column_from_url: Import LinkedIn person profiles
    - upsert_linkedin_company_profile_column_from_url: Import LinkedIn company profiles
    - upsert_phone_number_column: Discover phone numbers
    - upsert_work_email_column: Find work emails

    Content Agent:
    - upsert_text_column: Create text/formula columns
    - upsert_ai_text_column: AI-generated content
    - upsert_bond_ai_researcher_column: Research insights
    - upsert_ai_message_copywriter: Personalized messaging

    Data Management Agent:
    - read_table_data: Fetch table data with filtering
    - read_user_view_table_filters: Get current filters
    - update_user_view_table_filters_tool: Update table filters

    Execution Agent:
    - run_column: Execute smart columns with monitoring

    Create a JSON execution plan with the following structure:
    {{
        "id": "unique_plan_id",
        "user_request": "original request",
        "tasks": [
            {{
                "id": "task_1",
                "type": "research|enrichment|content|data_management|execution",
                "agent": "agent_name",
                "description": "detailed task description",
                "tools": ["tool1", "tool2"],
                "dependencies": ["task_id_if_any"],
                "priority": "high|medium|low",
                "context": {{"key": "value"}},
                "expected_output": "what this task should produce"
            }}
        ],
        "estimated_duration": 120,
        "success_criteria": ["criterion1", "criterion2"],
        "fallback_strategies": ["strategy1", "strategy2"]
    }}

    PLANNING GUIDELINES:
    1. Break complex requests into logical, sequential tasks
    2. Assign tasks to agents based on their tool capabilities
    3. Define clear dependencies between tasks
    4. Include validation and error handling steps
    5. Estimate realistic execution times
    6. Provide fallback strategies for critical failures
    """

    try:
        # Get planning response from model
        planning_response = model.invoke([HumanMessage(planning_prompt)], config)

        # Parse the JSON response
        import json
        plan_data = json.loads(planning_response.content)

        # Validate and create ExecutionPlan object
        execution_plan = ExecutionPlan(**plan_data)

        return {
            "execution_plan": execution_plan.model_dump(),
            "messages": [AIMessage(f"Created execution plan with {len(execution_plan.tasks)} tasks")]
        }

    except Exception as e:
        return {
            "execution_plan": None,
            "error": f"Planning failed: {str(e)}",
            "messages": [AIMessage(f"Planning failed: {str(e)}")]
        }
```

## Complete Feature Coverage - Tool Mapping

### Current Tool Distribution (AGENT_TOOLS_MAP)

The current implementation uses `AGENT_TOOLS_MAP` from the registry module, which maps `AgentName` enums to actual tool objects:

```python
from bond_ai.registry.registry import AGENT_TOOLS_MAP, AgentName

# Current tool mapping structure
AGENT_TOOLS_MAP = {
    AgentName.agent_research: [
        search_linkedin_profiles,                                   # LinkedIn profile discovery
        search,                                                     # Web search for research
        scrape_website                                              # Website content extraction
    ],
    AgentName.enrichment_agent: [
        upsert_linkedin_person_profile_column_from_url,             # LinkedIn person import
        upsert_linkedin_company_profile_column_from_url,            # LinkedIn company import
        upsert_phone_number_column,                                 # Phone discovery
        upsert_work_email_column                                    # Email discovery
    ],
    AgentName.content_agent: [
        upsert_text_column,                                         # Static text/formula columns
        upsert_ai_text_column,                                      # AI-generated content
        upsert_bond_ai_researcher_column,                           # Research insights
        upsert_ai_message_copywriter                                # Personalized messaging
    ],
    AgentName.data_management_agent: [
        read_table_data,                                            # Table data retrieval
        read_user_view_table_filters,                               # Filter management
        update_user_view_table_filters_tool                         # Filter updates
    ],
    AgentName.execution_agent: [  # HITL ALWAYS
       run_column                                                   # Column execution and monitoring
    ],
}

# Agent name enumeration
class AgentName(Enum):
    agent_research = "research_agent"
    enrichment_agent = "enrichment_agent"
    content_agent = "content_agent"
    data_management_agent = "data_management_agent"
    execution_agent = "execution_agent"
```

# Current agent creation pattern using registry
def _create_react_agent(name: str, system_prompt: str, tools: list, llm=None):
    """Factory function to create a ReAct agent with system prompt and tools."""
    if llm is None:
        llm = ChatOpenAI(model="gpt-4o-mini")

    prompt = ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        MessagesPlaceholder(variable_name="messages")
    ])

    agent = create_react_agent(
        model=llm,
        tools=tools,
        prompt=prompt)
    return agent

def create_workflow_from_registry():
    """Create workflow using the agent registry."""
    workflow = StateGraph(BondAIAgentState)

    workflow.add_node("table_indexing", table_indexing_node)
    workflow.add_edge("table_indexing", "supervisor")

    # Create ReAct agents from registry
    react_agents = supervisor_agent_registry.get_react_agents()
    for name, config in react_agents.items():
        # Filter config to only include parameters that _create_react_agent expects
        agent_params = {
            "name": name,
            "system_prompt": config["system_prompt"],
            "tools": config["tools"]
        }
        # Add llm if specified in config
        if "llm" in config:
            agent_params["llm"] = config["llm"]

        node = _create_react_agent(**agent_params)
        workflow.add_node(name, node)

    # Add custom nodes from registry
    custom_nodes = supervisor_agent_registry.get_custom_nodes()
    for name, config in custom_nodes.items():
        node_function = config["node_function"]
        workflow.add_node(name, node_function)

    # Add supervisor and utility nodes
    workflow.add_node("supervisor", supervisor_agent_anthropic)
    workflow.add_node("error_response", error_response_node)
    workflow.add_node("task_completion", task_completion_node)

    # Connect all agents/nodes to supervisor
    current_members = supervisor_agent_registry.get_agent_names()
    for member in current_members:
        workflow.add_edge(member, "supervisor")

    # Create conditional routing
    conditional_map = {k: k for k in current_members}
    conditional_map["FINISH"] = END
    conditional_map["error_response"] = "error_response"
    conditional_map["task_completion"] = "task_completion"
    workflow.add_conditional_edges("supervisor", lambda x: x["next"], conditional_map)

    workflow.set_entry_point("table_indexing")
    return workflow.compile()
```

### Use Case Coverage Validation

```python
# Comprehensive use case mapping to ensure all current functionality is preserved
USE_CASE_COVERAGE = {
    "prospect_research": {
        "primary_agent": "research_agent",
        "tools": ["search", "scrape_website", "search_linkedin_profiles"],
        "workflow": "Research → Discovery → Validation"
    },

    "data_enrichment": {
        "primary_agent": "enrichment_agent",
        "tools": ["upsert_linkedin_person_profile_column_from_url",
                 "upsert_phone_number_column", "upsert_work_email_column"],
        "workflow": "Profile Import → Contact Discovery → Validation"
    },

    "content_generation": {
        "primary_agent": "content_agent",
        "tools": ["upsert_ai_text_column", "upsert_bond_ai_researcher_column",
                 "upsert_ai_message_copywriter"],
        "workflow": "Research → Content Creation → Personalization"
    },

    "table_management": {
        "primary_agent": "data_management_agent",
        "tools": ["read_table_data", "read_user_view_table_filters",
                 "update_user_view_table_filters_tool"],
        "workflow": "Analysis → Filtering → Optimization"
    },

    "execution_monitoring": {
        "primary_agent": "execution_agent",
        "tools": ["run_column"],
        "workflow": "Validation → Execution → Monitoring → Reporting"
    }
}
```

## Current LangGraph Implementation

### Current BondAIAgentState Structure

```python
from typing import Annotated, List, Sequence, TypedDict, Optional
from langchain_core.messages import BaseMessage
from langgraph.graph.message import add_messages
from bond_ai.models.planner_model import Task

# State reducers
def tasks_reducer(current: List[Task], new: List[Task]) -> List[Task]:
    """Reducer for tasks list - merge by ID, keeping latest version."""
    if not current:
        return new
    if not new:
        return current

    # Create a dict for efficient lookup
    current_dict = {task.id: task for task in current}

    # Update with new tasks
    for task in new:
        current_dict[task.id] = task

    return list(current_dict.values())

def add_tool_calls(existing: Optional[List[dict]], new: Optional[List[dict]]) -> List[dict]:
    """Reducer that accumulates tool calls instead of replacing them."""
    if existing is None:
        existing = []
    if new is None:
        new = []
    return existing + new

def preserve_table_summary(existing: Optional[str], new: Optional[str]) -> Optional[str]:
    """Preserve existing table summary unless explicitly updated."""
    return new if new is not None else existing

class BondAIAgentState(TypedDict):
    """The state of the agent.

    This defines the structure of data flowing through the ReAct agent.
    """
    # Core message handling
    messages: Annotated[Sequence[BaseMessage], add_messages]

    # Table context
    table_summary: Annotated[Optional[str], preserve_table_summary]
    mode: Optional[str]
    selected_row_ids: Optional[int]
    selected_column_ids: Optional[str]
    intents: Optional[List[str]]
    tool_calls: Annotated[Optional[List[dict]], add_tool_calls]

    # Planning and execution
    plan_tasks: Annotated[List[Task], tasks_reducer]
    active_task_id: Optional[str]   # ID of currently executing task

    # Supervisor routing
    next: Optional[str]   # Next node to run

    # Error handling
    last_error_message: Optional[str]
```

### Current Graph Implementation Pattern

The current implementation uses `create_workflow_from_registry()` which dynamically builds the graph from the agent registry:

```python
def create_workflow_from_registry():
    """Create workflow using the agent registry."""
    workflow = StateGraph(BondAIAgentState)

    # Add core nodes
    workflow.add_node("table_indexing", table_indexing_node)
    workflow.add_edge("table_indexing", "supervisor")

    # Create ReAct agents from registry
    react_agents = supervisor_agent_registry.get_react_agents()
    for name, config in react_agents.items():
        agent_params = {
            "name": name,
            "system_prompt": config["system_prompt"],
            "tools": config["tools"]
        }
        if "llm" in config:
            agent_params["llm"] = config["llm"]

        node = _create_react_agent(**agent_params)
        workflow.add_node(name, node)

    # Add custom nodes from registry
    custom_nodes = supervisor_agent_registry.get_custom_nodes()
    for name, config in custom_nodes.items():
        node_function = config["node_function"]
        workflow.add_node(name, node_function)

    # Add supervisor and utility nodes
    workflow.add_node("supervisor", supervisor_agent_anthropic)
    workflow.add_node("error_response", error_response_node)
    workflow.add_node("task_completion", task_completion_node)

    # Connect all agents/nodes to supervisor
    current_members = supervisor_agent_registry.get_agent_names()
    for member in current_members:
        workflow.add_edge(member, "supervisor")

    # Connect utility nodes
    workflow.add_edge("error_response", END)
    workflow.add_edge("task_completion", "supervisor")

    # Create conditional routing
    conditional_map = {k: k for k in current_members}
    conditional_map["FINISH"] = END
    conditional_map["error_response"] = "error_response"
    conditional_map["task_completion"] = "task_completion"
    workflow.add_conditional_edges("supervisor", lambda x: x["next"], conditional_map)

    workflow.set_entry_point("table_indexing")
    return workflow.compile()

# Create the initial workflow
graph = create_workflow_from_registry()
```

### Legacy Workflow Definition (for reference)

```python
from langgraph.graph import StateGraph, END, START
from langgraph.types import interrupt
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage, ToolMessage

def supervisor_node(state: SupervisorAgentState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Supervisor node that orchestrates task delegation and monitors execution.
    """
    configuration = Configuration.from_runnable_config(config)

    # Check if we have an execution plan
    execution_plan = state.get("execution_plan")
    if not execution_plan:
        # No plan exists, delegate to planner
        return {
            "messages": [AIMessage("Creating execution plan...")],
            "active_agent": "planner"
        }

    # Check current task status
    current_task_id = state.get("current_task_id")
    task_status = state.get("task_status", [])

    # Find next task to execute
    next_task = None
    for task in execution_plan.get("tasks", []):
        task_id = task.get("id")

        # Check if task is pending and dependencies are met
        if task.get("status") == "pending":
            dependencies = task.get("dependencies", [])
            dependencies_met = all(
                any(t.get("id") == dep_id and t.get("status") == "completed"
                    for t in task_status)
                for dep_id in dependencies
            ) if dependencies else True

            if dependencies_met:
                next_task = task
                break

    if not next_task:
        # All tasks completed or no executable tasks
        return {
            "messages": [AIMessage("All tasks completed successfully!")],
            "active_agent": None
        }

    # Delegate to appropriate agent
    agent_name = next_task.get("agent")
    task_description = next_task.get("description")

    # Update task status to in_progress
    updated_status = task_status.copy() if task_status else []
    for task in updated_status:
        if task.get("id") == next_task.get("id"):
            task["status"] = "in_progress"
            break
    else:
        updated_status.append({
            "id": next_task.get("id"),
            "status": "in_progress",
            "agent": agent_name
        })

    # Create delegation message
    delegation_message = f"""
    TASK DELEGATION:
    Agent: {agent_name}
    Task: {task_description}
    Tools: {next_task.get('tools', [])}
    Context: {next_task.get('context', {})}
    Expected Output: {next_task.get('expected_output', '')}
    """

    return {
        "messages": [AIMessage(delegation_message)],
        "current_task_id": next_task.get("id"),
        "task_status": updated_status,
        "active_agent": agent_name
    }

def specialized_agent_node(agent_name: str):
    """
    Factory function to create specialized agent nodes.
    """
    def agent_node(state: SupervisorAgentState, config: RunnableConfig) -> Dict[str, Any]:
        configuration = Configuration.from_runnable_config(config)

        # Get agent configuration
        agents = create_specialized_agents(tools_by_name)
        agent_config = agents.get(agent_name)

        if not agent_config:
            return {
                "messages": [AIMessage(f"Error: Agent {agent_name} not found")],
                "error_context": {"agent": agent_name, "error": "Agent not found"}
            }

        # Get current task context
        current_task_id = state.get("current_task_id")
        execution_plan = state.get("execution_plan", {})
        current_task = None

        for task in execution_plan.get("tasks", []):
            if task.get("id") == current_task_id:
                current_task = task
                break

        if not current_task:
            return {
                "messages": [AIMessage(f"Error: No current task found for {agent_name}")],
                "error_context": {"agent": agent_name, "error": "No current task"}
            }

        # Create agent-specific system prompt
        system_prompt = agent_config["prompt"].format(
            table_summary=state.get("table_summary", ""),
            current_task=current_task.get("description", ""),
            task_context=current_task.get("context", {}),
            expected_output=current_task.get("expected_output", "")
        )

        # Prepare messages for agent
        messages = [
            SystemMessage(system_prompt),
            *state.get("messages", [])[-3:]  # Last 3 messages for context
        ]

        try:
            # Call specialized agent
            model = agent_config["model"]
            response = model.invoke(messages, config)

            # Check if agent made tool calls
            if hasattr(response, 'tool_calls') and response.tool_calls:
                # Execute tools
                tool_outputs = []
                for tool_call in response.tool_calls:
                    try:
                        tool = tools_by_name[tool_call["name"]]
                        tool_result = tool.invoke(tool_call["args"])
                        tool_outputs.append(ToolMessage(
                            content=str(tool_result),
                            name=tool_call["name"],
                            tool_call_id=tool_call["id"]
                        ))
                    except Exception as e:
                        tool_outputs.append(ToolMessage(
                            content=f"Error: {str(e)}",
                            name=tool_call["name"],
                            tool_call_id=tool_call["id"]
                        ))

                # Update task status to completed
                task_status = state.get("task_status", [])
                for task in task_status:
                    if task.get("id") == current_task_id:
                        task["status"] = "completed"
                        break

                return {
                    "messages": [response] + tool_outputs,
                    "task_status": task_status,
                    "agent_outputs": {agent_name: tool_outputs}
                }
            else:
                # Agent provided response without tools
                return {
                    "messages": [response],
                    "agent_outputs": {agent_name: response.content}
                }

        except Exception as e:
            # Handle agent execution error
            error_message = f"Agent {agent_name} execution failed: {str(e)}"

            # Update task status to failed
            task_status = state.get("task_status", [])
            for task in task_status:
                if task.get("id") == current_task_id:
                    task["status"] = "failed"
                    task["error"] = str(e)
                    break

            return {
                "messages": [AIMessage(error_message)],
                "task_status": task_status,
                "error_context": {"agent": agent_name, "error": str(e)}
            }

    return agent_node

def should_continue_supervisor(state: SupervisorAgentState) -> str:
    """
    Determine the next node in the supervisor workflow.
    """
    active_agent = state.get("active_agent")

    if active_agent == "planner":
        return "planner"
    elif active_agent in ["research_agent", "enrichment_agent", "content_agent",
                         "data_management_agent", "execution_agent"]:
        return active_agent
    elif active_agent is None:
        return END
    else:
        return "supervisor"

def create_supervisor_graph() -> StateGraph:
    """
    Create the complete supervisor agentic pattern graph.
    """
    # Create the graph with enhanced state
    workflow = StateGraph(SupervisorAgentState, config_schema=Configuration)

    # Add nodes
    workflow.add_node("table_indexing", table_indexing_node)  # Preserved from original
    workflow.add_node("supervisor", supervisor_node)
    workflow.add_node("planner", planner_node)

    # Add specialized agent nodes
    workflow.add_node("research_agent", specialized_agent_node("research_agent"))
    workflow.add_node("enrichment_agent", specialized_agent_node("enrichment_agent"))
    workflow.add_node("content_agent", specialized_agent_node("content_agent"))
    workflow.add_node("data_management_agent", specialized_agent_node("data_management_agent"))
    workflow.add_node("execution_agent", specialized_agent_node("execution_agent"))

    # Define workflow edges
    workflow.set_entry_point("table_indexing")
    workflow.add_edge("table_indexing", "supervisor")

    # Conditional edges from supervisor
    workflow.add_conditional_edges(
        "supervisor",
        should_continue_supervisor,
        {
            "planner": "planner",
            "research_agent": "research_agent",
            "enrichment_agent": "enrichment_agent",
            "content_agent": "content_agent",
            "data_management_agent": "data_management_agent",
            "execution_agent": "execution_agent",
            END: END
        }
    )

    # All specialized agents return to supervisor
    workflow.add_edge("planner", "supervisor")
    workflow.add_edge("research_agent", "supervisor")
    workflow.add_edge("enrichment_agent", "supervisor")
    workflow.add_edge("content_agent", "supervisor")
    workflow.add_edge("data_management_agent", "supervisor")
    workflow.add_edge("execution_agent", "supervisor")

    # Compile the graph
    graph = workflow.compile()
    graph.name = "Supervisor Agentic Pattern"

    return graph

# Create the supervisor graph instance
supervisor_graph = create_supervisor_graph()
```

## Sequence Diagrams - Supervisor-to-Agent Task Flows

### Scenario 1: Complex Prospect Research and Enrichment

```mermaid
sequenceDiagram
    participant User
    participant Supervisor
    participant Planner
    participant Research
    participant Enrichment
    participant Content
    participant Execution

    User->>Supervisor: "Find 50 marketing managers at SaaS companies and create personalized outreach"
    Supervisor->>Planner: Create execution plan
    Planner-->>Supervisor: Plan with 5 tasks across 4 agents

    Supervisor->>Research: Task 1: Find LinkedIn profiles
    Research->>Research: search_linkedin_profiles
    Research-->>Supervisor: 50 profiles discovered

    Supervisor->>Enrichment: Task 2: Import LinkedIn profiles
    Enrichment->>Enrichment: upsert_linkedin_person_profile_column_from_url
    Enrichment-->>Supervisor: Profile column created

    Supervisor->>Enrichment: Task 3: Discover work emails
    Enrichment->>Enrichment: upsert_work_email_column
    Enrichment-->>Supervisor: Email column created

    Supervisor->>Content: Task 4: Create personalized messages
    Content->>Content: upsert_ai_message_copywriter
    Content-->>Supervisor: Message column created

    Supervisor->>Execution: Task 5: Execute all columns
    Execution->>User: Confirm execution for 50 rows?
    User-->>Execution: Confirmed
    Execution->>Execution: run_column (multiple)
    Execution-->>Supervisor: All columns executed successfully

    Supervisor->>User: "Campaign ready: 50 prospects with personalized messages"
```

### Scenario 2: Data Analysis and Filtering

```mermaid
sequenceDiagram
    participant User
    participant Supervisor
    participant Planner
    participant DataMgmt
    participant Research

    User->>Supervisor: "Show me prospects from companies with 100+ employees in tech industry"
    Supervisor->>Planner: Create analysis plan
    Planner-->>Supervisor: Plan with data filtering and research validation

    Supervisor->>DataMgmt: Task 1: Analyze current table state
    DataMgmt->>DataMgmt: read_user_view_table_filters
    DataMgmt->>DataMgmt: read_table_data (summary)
    DataMgmt-->>Supervisor: Current state analysis

    Supervisor->>DataMgmt: Task 2: Apply company size filters
    DataMgmt->>DataMgmt: update_user_view_table_filters_tool
    DataMgmt-->>Supervisor: Filters applied

    Supervisor->>Research: Task 3: Validate company information
    Research->>Research: search (company verification)
    Research-->>Supervisor: Company data validated

    Supervisor->>DataMgmt: Task 4: Generate filtered dataset
    DataMgmt->>DataMgmt: read_table_data (filtered)
    DataMgmt-->>Supervisor: Filtered results ready

    Supervisor->>User: "Found 23 prospects from tech companies 100+ employees"
```

## Current Best Practices and Implementation Patterns

### Task-Based Dispatching with supervisor_agent_anthropic

The current implementation uses `supervisor_agent_anthropic` which provides enhanced task-aware dispatching:

```python
def supervisor_agent_anthropic(state):
    """Enhanced supervisor agent implementation with task-aware dispatching.

    This supervisor:
    1. Checks for existing execution plans and dispatches tasks sequentially
    2. Updates task status as tasks are assigned and completed
    3. Handles task dependencies and execution order
    4. Provides clear task instructions to agents
    5. Tracks progress and handles failures gracefully
    """
    # Check if there's an error message in the state
    if state.get("last_error_message"):
        return {"next": "error_response"}

    # Get current team members dynamically
    current_members = supervisor_agent_registry.get_agent_names()
    current_options = ["FINISH"] + current_members

    # Check if we have an execution plan with tasks
    plan_tasks = state.get("plan_tasks", [])
    active_task_id = state.get("active_task_id")

    # If we have tasks, handle task-based dispatching
    if plan_tasks:
        return _handle_task_based_dispatching(state, plan_tasks, active_task_id, current_members)

    # If no tasks, fall back to original supervisor behavior
    return _handle_general_routing(state, current_members, current_options)
```

### Error Handling with last_error_message

The system includes robust error handling using the `last_error_message` state field:

```python
def error_response_node(state):
    """Node that handles error messages and informs the user before ending."""
    from langchain_core.messages import AIMessage

    # Get the error message from state
    error_message = state.get("last_error_message", "An unexpected error occurred.")

    # Create a user-friendly error response
    user_message = f"I encountered an issue while processing your request: {error_message}\n\nPlease try rephrasing your request or contact support if the issue persists."

    # Return the error message and set next to FINISH to end the conversation
    return {
        "messages": [AIMessage(content=user_message)],
        "next": "FINISH"
    }
```

### Agent Registry Management

The current system provides utility functions for easy agent management:

```python
def add_agent(name: str, system_prompt: str, tools: list, description: str, enabled: bool = True):
    """Add a new ReAct agent to the registry."""
    supervisor_agent_registry.register_agent(name, system_prompt, tools, description, enabled)

def add_node(name: str, node_function, description: str, enabled: bool = True, **kwargs):
    """Add a pre-built node function to the registry."""
    supervisor_agent_registry.register_node_prebuild(name, node_function, description, enabled, **kwargs)

def rebuild_workflow():
    """Rebuild the workflow with current registry state."""
    global graph
    current_agents = supervisor_agent_registry.get_agent_names()
    graph = create_workflow_from_registry()
    return graph
```

### Error Handling and User Experience Preservation

```python
def preserve_user_confirmations(state: SupervisorAgentState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Ensure user confirmations are preserved across agent transitions.
    """
    pending_confirmations = state.get("pending_confirmations", [])

    if pending_confirmations:
        # Handle pending confirmations before proceeding
        for confirmation in pending_confirmations:
            if confirmation.get("type") == "run_column":
                # Use existing interrupt mechanism
                user_response = interrupt(confirmation.get("message"))

                if user_response and str(user_response).lower() in ['yes', 'y', 'confirm', 'proceed', 'ok', 'run']:
                    # User confirmed, proceed with execution
                    confirmation["status"] = "confirmed"
                else:
                    # User cancelled, update task status
                    confirmation["status"] = "cancelled"

    return {"pending_confirmations": pending_confirmations}

def maintain_stream_writing(agent_name: str, action: str) -> None:
    """
    Preserve real-time status updates across all agents.
    """
    from langgraph.config import get_stream_writer

    stream_writer = get_stream_writer()
    stream_writer({
        "custom_tool_call": f"[{agent_name}] {action}",
        "agent": agent_name,
        "timestamp": datetime.now().isoformat()
    })
```

## Performance Optimizations and Monitoring

### Execution Metrics

```python
class SupervisorMetrics:
    """
    Performance monitoring for supervisor agentic pattern.
    """

    def __init__(self):
        self.task_execution_times = {}
        self.agent_performance = {}
        self.error_rates = {}
        self.user_satisfaction = {}

    def track_task_execution(self, task_id: str, agent: str, duration: float, success: bool):
        """Track individual task performance."""
        if agent not in self.agent_performance:
            self.agent_performance[agent] = {
                "total_tasks": 0,
                "successful_tasks": 0,
                "average_duration": 0,
                "error_count": 0
            }

        metrics = self.agent_performance[agent]
        metrics["total_tasks"] += 1
        if success:
            metrics["successful_tasks"] += 1
        else:
            metrics["error_count"] += 1

        # Update average duration
        current_avg = metrics["average_duration"]
        total_tasks = metrics["total_tasks"]
        metrics["average_duration"] = ((current_avg * (total_tasks - 1)) + duration) / total_tasks

    def get_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        return {
            "agent_performance": self.agent_performance,
            "total_tasks_executed": sum(m["total_tasks"] for m in self.agent_performance.values()),
            "overall_success_rate": sum(m["successful_tasks"] for m in self.agent_performance.values()) /
                                  max(sum(m["total_tasks"] for m in self.agent_performance.values()), 1),
            "average_execution_time": sum(m["average_duration"] for m in self.agent_performance.values()) /
                                    max(len(self.agent_performance), 1)
        }

# Global metrics instance
supervisor_metrics = SupervisorMetrics()
```

## Summary

This Supervisor Agentic Pattern implementation provides:

1. **Flexible Agent Registry**: Support for both ReAct agents and custom node functions with easy management utilities
2. **Current Tool Coverage**: All tools mapped to specialized agents through AGENT_TOOLS_MAP with proper enum-based organization
3. **Enhanced Task Management**: Sophisticated task breakdown using the current Task model with proper state reducers
4. **Robust Error Handling**: Comprehensive error recovery using last_error_message and dedicated error response nodes
5. **Task-Based Dispatching**: Advanced supervisor logic with _handle_task_based_dispatching for complex workflows
6. **Modular Architecture**: Clean separation between ReAct agents and custom nodes enabling easy extensibility
7. **Current State Management**: Proper BondAIAgentState with reducers for messages, tasks, and tool calls
8. **Enhanced Planner**: Sophisticated planner with JSON schema output following the Canonical Eight-Phase Flow

The pattern enables complex multi-step workflows while maintaining the flexibility and modularity of the current agent registry system. The implementation follows current best practices with proper error handling, state management, and agent coordination.

## Usage Examples

### Adding a New Agent
```python
from bond_ai.graph import add_agent
from bond_ai.tools import search, scrape_website

add_agent(
    name="custom_research_agent",
    system_prompt="You are a specialized research agent...",
    tools=[search, scrape_website],
    description="Custom research agent for specific use cases"
)
```

### Adding a Custom Node
```python
from bond_ai.graph import add_node

def custom_validation_node(state, config):
    """Custom validation logic."""
    return {"messages": [{"content": "Validation complete"}]}

add_node(
    name="CustomValidator",
    node_function=custom_validation_node,
    description="Validates data integrity"
)
```

### Rebuilding the Workflow
```python
from bond_ai.graph import rebuild_workflow

# After adding agents or nodes
graph = rebuild_workflow()
```
