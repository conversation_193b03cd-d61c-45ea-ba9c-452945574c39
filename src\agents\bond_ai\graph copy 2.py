from dotenv import load_dotenv
from langchain.chat_models import init_chat_model
from langgraph.prebuilt import create_react_agent
from langgraph.prebuilt import create_react_agent

from agent.utils import load_chat_model


load_dotenv()

def get_weather(city: str) -> str:  
    """Get weather for a given city."""
    return f"It's always sunny in {city}!"

llm = load_chat_model("bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0")
agent = create_react_agent(
    model=llm,  
    tools=[get_weather],  
    prompt="You are a helpful assistant"  
)


from typing import Annotated

from typing_extensions import TypedDict

from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages


class State(TypedDict):
    # Messages have the type "list". The `add_messages` function
    # in the annotation defines how this state key should be updated
    # (in this case, it appends messages to the list, rather than overwriting them)
    messages: Annotated[list, add_messages]
    
    
graph_builder = StateGraph(State)

graph_builder.add_node("agent", agent)
graph_builder.set_entry_point("agent")
graph_builder.add_edge("agent", END)

graph = graph_builder.compile()


