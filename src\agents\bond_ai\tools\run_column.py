"""Column execution tool."""

from typing import <PERSON><PERSON>, <PERSON><PERSON>
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool
from langgraph.config import get_stream_writer
from bond_ai.configuration import Configuration
import json
import time

from ..agent_db import db_run_column, poll_cell_until_complete
from .read_table_data import read_table_data


@tool
def run_column(
    config: RunnableConfig,
    column_id: str,
    column_name: str,
    count: int = 1,
    row_id: int = 1,
    wait_results: bool = False
) -> Tuple[Optional[str], Optional[str]]:
    """Run a specific column in a table to process data and wait for completion.
    
    This tool triggers the execution of a smart column for the specified table.
    It will process the number of rows specified by count and optionally wait for completion.
    
    Parameters:
        config: Configuration injected by the system
        column_id: The ID of the column to run
        column_name: The name of the column to run
        count: Optional number of rows to process. If not provided, 1 row will be processed.
        row_id: The row ID to monitor for completion status (defaults to 1, only used when count=1)
        wait_results: If True, wait for all requested cells to complete regardless of count (defaults to False)
        
    Returns:
        Tuple[Optional[str], Optional[str]]: When wait_results=False, returns tuple containing 
        (success_message_with_data, error_message). When wait_results=True, returns tuple containing 
        (execution_summary, error_message).
    """
    try:
        # All logic with side effects comes AFTER the interrupt
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"Running {column_name} column"})
        
        # Get configuration
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        
        # First, check if the column is runnable
        print(f"🔍 Checking if column {column_id} is runnable...")
        table_data, read_error = read_table_data.invoke({
            "config": config,
            "max_rows": 1,
            "summarize": False,
            "column_ids": [int(column_id)]
        })
        
        if read_error:
            return None, f"Error checking column runnability: {read_error}"
        
        if not table_data:
            return None, f"Could not retrieve column information for column {column_id}"
        
        
        # Check if column is runnable - handle raw table data structure
        # The table_data is the direct response from get_table_data which should contain 'columns' key
        columns = table_data.get('columns', [])
        
        target_column = None
        for col in columns:
            if col.get('column_id') == int(column_id):
                target_column = col
                break
        
        if not target_column:
            return None, f"Column {column_id} not found in table"
        
        if not target_column.get('is_runnable', False):
            return None, f"Column '{target_column.get('column_name', column_id)}' (ID: {column_id}) is not runnable. Only smart columns like AI, Research, Email finder, Phone finder, etc. can be run."
        
        # Call the db_run_column function from agent_db.py
        success, error = db_run_column(
            table_id=table_id,
            column_id=column_id,
            count=count
        )
        
        if error:
            return None, error
        
        # Wait for completion only if wait_results is True
        if wait_results:
            stream_writer = get_stream_writer()
            stream_writer({"custom_tool_call": f"Waiting for column enrichment to complete"})
            # Start polling the cell(s) until completion
            max_attempts = 50  # Maximum 50 seconds
            attempt = 0
            
            # Determine which rows to poll
            if count == 1:
                rows_to_poll = [row_id]
            else:
                # When count > 1, poll rows 1 through count
                rows_to_poll = list(range(1, count + 1))
            
            while attempt < max_attempts:
                completed_cells = []
                failed_cells = []
                awaiting_input_cells = []
                still_processing = []
                
                # Check status of all rows
                for current_row_id in rows_to_poll:
                    cell_data, poll_error = poll_cell_until_complete(table_id, current_row_id, int(column_id))
                    
                    if poll_error:
                        print(f"❌ Error polling row {current_row_id}: {poll_error}")
                        return None, f"Error polling cell status for row {current_row_id}: {poll_error}"
                    
                    if cell_data:
                        run_status = cell_data.get('run')
                        print(f"📊 Row {current_row_id}: status = '{run_status}', cell_data = {cell_data}")
                        
                        if run_status == 'completed':
                            completed_cells.append((current_row_id, cell_data))
                            print(f"✅ Row {current_row_id}: COMPLETED")
                        elif run_status == 'failed':
                            failed_cells.append((current_row_id, cell_data))
                            print(f"❌ Row {current_row_id}: FAILED")
                        elif run_status == 'awaiting_input':
                            awaiting_input_cells.append((current_row_id, cell_data))
                            print(f"⏳ Row {current_row_id}: AWAITING_INPUT")
                        else:
                            still_processing.append(current_row_id)
                            print(f"🔄 Row {current_row_id}: STILL_PROCESSING (status: '{run_status}')")
                    else:
                        still_processing.append(current_row_id)
                        print(f"❓ Row {current_row_id}: NO_DATA - added to still_processing")
                
                
                # Check if all cells have reached an end status
                if not still_processing:
                    # All cells have completed processing
                    total_cells = len(rows_to_poll)
                    completed_count = len(completed_cells)
                    failed_count = len(failed_cells)
                    awaiting_input_count = len(awaiting_input_cells)
                    
                    # Prepare execution summary
                    if count == 1:
                        # For single cell, prepare detailed result
                        if completed_cells:
                            execution_summary = f"Successfully ran column {column_id} in table {table_id} for 1 row. Final data: {completed_cells[0][1]}"
                        elif failed_cells:
                            # Extract status message from failed cell
                            failed_cell_data = failed_cells[0][1]
                            status_message = "No status message available"
                            if isinstance(failed_cell_data, dict):
                                # Look for message in different possible locations
                                if 'run_status' in failed_cell_data and isinstance(failed_cell_data['run_status'], dict):
                                    status_message = failed_cell_data['run_status'].get('message', status_message)
                                elif 'message' in failed_cell_data:
                                    status_message = failed_cell_data['message']
                            return None, f"Column execution failed. Status message: {status_message}. Full cell data: {failed_cell_data}"
                        elif awaiting_input_cells:
                            # Extract status message from awaiting input cell
                            awaiting_cell_data = awaiting_input_cells[0][1]
                            status_message = "No status message available"
                            if isinstance(awaiting_cell_data, dict):
                                # Look for message in different possible locations
                                if 'run_status' in awaiting_cell_data and isinstance(awaiting_cell_data['run_status'], dict):
                                    status_message = awaiting_cell_data['run_status'].get('message', status_message)
                                elif 'message' in awaiting_cell_data:
                                    status_message = awaiting_cell_data['message']
                            return None, f"Column execution is awaiting input. Status message: {status_message}. Full cell data: {awaiting_cell_data}"
                    else:
                        # For multiple cells, prepare summary
                        execution_summary = f"Successfully ran column {column_id} in table {table_id} for {count} rows. "
                        execution_summary += f"Results: {completed_count} completed, {failed_count} failed, {awaiting_input_count} awaiting input."
                        
                        if failed_count > 0 or awaiting_input_count > 0:
                            # Include details about non-completed cells
                            details = []
                            if failed_count > 0:
                                failed_details = []
                                for row_id, cell_data in failed_cells:
                                    status_message = "No message"
                                    if isinstance(cell_data, dict):
                                        if 'run_status' in cell_data and isinstance(cell_data['run_status'], dict):
                                            status_message = cell_data['run_status'].get('message', status_message)
                                        elif 'message' in cell_data:
                                            status_message = cell_data['message']
                                    failed_details.append(f"row {row_id} ({status_message})")
                                details.append(f"Failed: {', '.join(failed_details)}")
                            if awaiting_input_count > 0:
                                awaiting_details = []
                                for row_id, cell_data in awaiting_input_cells:
                                    status_message = "No message"
                                    if isinstance(cell_data, dict):
                                        if 'run_status' in cell_data and isinstance(cell_data['run_status'], dict):
                                            status_message = cell_data['run_status'].get('message', status_message)
                                        elif 'message' in cell_data:
                                            status_message = cell_data['message']
                                    awaiting_details.append(f"row {row_id} ({status_message})")
                                details.append(f"Awaiting input: {', '.join(awaiting_details)}")
                            execution_summary += f" Details: {'; '.join(details)}"
                    
                    # Create JSON response
                    response_json = {
                        "count": count,
                        "column_name": column_name,
                        "message": execution_summary
                    }
                    
                    # Return tuple containing execution summary and error message
                    return json.dumps(response_json), None
                
                # Wait 1 second before next poll
                time.sleep(1)
                attempt += 1
            
            # If we reach here, we've exceeded max attempts
            return None, f"Timeout: Column execution did not complete within {max_attempts} seconds"
        else:
            # Return success without polling when wait_results is False
            count_msg = f"for {count} rows" if count > 1 else "for 1 row"
            
            # Create JSON response
            response_json = {
                "count": count,
                "column_name": column_name,
                "message": f"Successfully triggered column {column_id} execution in table {table_id} {count_msg}. The data will be populated in the table automatically."
            }
            
            return json.dumps(response_json), None
        
    except Exception as e:
        return None, str(e)
