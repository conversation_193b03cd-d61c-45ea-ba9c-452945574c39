from typing import List, Literal, Optional
from pydantic import BaseModel, Field


class Task(BaseModel):
    """Task to be executed"""
    id: str = Field(description="unique id of the task")
    order: int = Field(description="order of the task")
    action: str = Field(description="task to be executed")
    tool: str = Field(description="tool to be used")
    agent: str = Field(description="assigned agent name based on tool capability")
    why: str = Field(description="reason for the task")
    status: Literal["pending", "in_progress", "completed","failed"] = Field(description="status of the task"
    )
    error: Optional[str] = Field(description="error message if any")

# class Plan(BaseModel):
#     """Plan to follow in future"""
#     objective: str = Field(description="objective of the plan")
#     tasks: List[Task] = Field(
#         description="different tasks to execute, should be in sorted order"
#     )
